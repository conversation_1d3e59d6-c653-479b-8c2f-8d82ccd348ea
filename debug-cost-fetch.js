/**
 * 费用数据获取诊断脚本
 * 检查定价信息是否正确从system_config传递到费用统计
 */

const cloudbase = require('@cloudbase/node-sdk')

const app = cloudbase.init({
  env: 'cloud1-4g85f8xlb8166ff1',
  region: 'ap-shanghai'
})

const db = app.database()

async function diagnoseCostData() {
  console.log('🔍 开始费用数据诊断...\n')

  try {
    // 1. 检查system_config中的定价配置
    console.log('📊 1. 检查system_config定价配置')
    const aiConfigs = await db.collection('system_config')
      .where({ type: 'ai_config' })
      .get()
    
    console.log(`找到 ${aiConfigs.data.length} 条AI配置:`)
    aiConfigs.data.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.provider}/${config.model}:
      输入价格: ${config.inputPrice}
      输出价格: ${config.outputPrice}
      状态: ${config.status}`)
    })

    // 2. 检查ai_usage记录是否包含定价字段
    console.log('\n📊 2. 检查ai_usage记录结构')
    const usageRecords = await db.collection('ai_usage').limit(5).get()
    
    console.log(`ai_usage记录: ${usageRecords.data.length} 条`)
    if (usageRecords.data.length > 0) {
      console.log('第一条记录结构:', Object.keys(usageRecords.data[0]))
      console.log('第一条定价字段:', {
        cost: usageRecords.data[0].cost,
        modelInputPrice: usageRecords.data[0].modelInputPrice,
        modelOutputPrice: usageRecords.data[0].modelOutputPrice,
        tokensUsed: usageRecords.data[0].tokensUsed
      })
    }

    // 3. 检查comments是否有定价信息
    console.log('\n📊 3. 检查comments集合格式')
    const comments = await db.collection('comments').limit(3).get()
    
    if (comments.data.length > 0) {
      console.log('comments样本:', comments.data.map(item => ({
        tokens: item.tokensUsed,
        contentLength: item.content?.length,
        teacherId: item.teacherId,
        createTime: item.createTime
      })))
    }

    // 4. 测试getCostStats云函数调用
    console.log('\n📊 4. 测试云函数费用统计')
    const getCostStats = await app.callFunction({
      name: 'getCostStats',
      data: { action: 'getAllCostStats' }
    })

    console.log('getCostStats返回:', {
      success: getCostStats.result?.success,
      total: getCostStats.result?.data?.total,
      teachers: getCostStats.result?.data?.teachers?.length || 0,
      trendData: getCostStats.result?.data?.trend?.length || 0
    })

    // 5. 验证实时定价与费用关联
    console.log('\n📊 5. 定价-费用关联验证')
    const activeAIConfig = await db.collection('system_config')
      .where({ type: 'ai_config', status: 'active' })
      .limit(1)
      .get()
    
    if (activeAIConfig.data.length > 0) {
      const config = activeAIConfig.data[0]
      console.log('当前活跃AI配置:', {
        model: config.model,
        inputPrice: config.inputPrice,
        outputPrice: config.outputPrice
      })
    }

  } catch (error) {
    console.error('❌ 诊断失败:', error)
  }
}

diagnoseCostData()