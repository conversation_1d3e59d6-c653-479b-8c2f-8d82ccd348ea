/**
 * AI配置模块处理器
 * 处理AI模型管理、提示词模板、使用统计等AI相关操作
 * Ultra-Think架构升级 - 统一模板系统支持
 */

// 内联工具函数，避免外部依赖
const generateChecksum = (content) => {
  if (!content) return 'empty'
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  return Math.abs(hash).toString()
}

const validateTemplate = (template) => {
  if (!template.name || !template.type || !template.content) {
    throw new Error('模板缺少必要字段')
  }
  return true
}

const safeTemplateUpdate = (current, updates) => {
  const backup = { ...current }
  const updated = { ...current, ...updates, version: (current.version || 1) + 1 }
  return { updatedTemplate: updated, backup }
}

module.exports = {
  /**
   * 获取AI模型列表
   */
  async getModels(data, db, cloud, admin) {
    const modelsResult = await db.collection('ai_configs').get()
    
    // 如果没有模型配置，返回默认豆包配置
    if (modelsResult.data.length === 0) {
      return [{
        id: 'doubao_default',
        name: '豆包AI（默认）',
        provider: 'doubao',
        model: 'ep-20241127201056-6whmp',
        status: 'active',
        config: {
          apiKey: '从云函数配置中读取',
          baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
          maxTokens: 2000,
          temperature: 0.7
        },
        createdAt: new Date().toISOString(),
        isDefault: true
      }]
    }
    
    return modelsResult.data.map(model => ({
      id: model._id,
      name: model.name,
      provider: model.provider,
      model: model.model,
      status: model.status,
      config: model.config,
      createdAt: model.createTime,
      updatedAt: model.updateTime,
      isDefault: model.isDefault || false
    }))
  },

  /**
   * 创建AI模型配置
   */
  async createModel(data, db, cloud, admin) {
    const { name, provider, model, config, inputPrice, outputPrice } = data

    if (!name || !provider || !model) {
      throw new Error('模型名称、提供商和模型标识不能为空')
    }

    // 检查名称是否已存在
    const existingModel = await db.collection('ai_configs').where({
      name: name
    }).get()

    if (existingModel.data.length > 0) {
      throw new Error('模型名称已存在')
    }

    const modelData = {
      name,
      provider,
      model,
      config: config || {},
      status: 'active',
      inputPrice: inputPrice || 0, // 输入定价
      outputPrice: outputPrice || 0, // 输出定价
      usage: 0, // 使用次数
      totalCost: 0, // 总费用
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      createTimestamp: Date.now(),
      updateTimestamp: Date.now(),
      createdBy: admin._id,
      isDefault: false
    }
    
    const result = await db.collection('ai_configs').add({
      data: modelData
    })
    
    return {
      id: result._id,
      ...modelData,
      message: '模型配置创建成功'
    }
  },

  /**
   * 更新AI模型配置
   */
  async updateModel(data, db, cloud, admin) {
    const { id, name, provider, model, config, status, inputPrice, outputPrice } = data

    if (!id) {
      throw new Error('模型ID不能为空')
    }

    const updateData = {
      updateTime: db.serverDate(),
      updateTimestamp: Date.now(),
      updatedBy: admin._id
    }

    if (name) updateData.name = name
    if (provider) updateData.provider = provider
    if (model) updateData.model = model
    if (config) updateData.config = config
    if (status) updateData.status = status
    if (inputPrice !== undefined) updateData.inputPrice = inputPrice
    if (outputPrice !== undefined) updateData.outputPrice = outputPrice
    
    await db.collection('ai_configs').doc(id).update({
      data: updateData
    })
    
    return {
      message: '模型配置更新成功'
    }
  },

  /**
   * 删除AI模型配置
   */
  async deleteModel(data, db, cloud, admin) {
    const { id } = data
    
    if (!id) {
      throw new Error('模型ID不能为空')
    }
    
    // 检查是否为默认模型
    const modelResult = await db.collection('ai_configs').doc(id).get()
    if (modelResult.data.length === 0) {
      throw new Error('模型配置不存在')
    }
    
    if (modelResult.data[0].isDefault) {
      throw new Error('不能删除默认模型配置')
    }
    
    await db.collection('ai_configs').doc(id).remove()
    
    return {
      message: '模型配置删除成功'
    }
  },

  /**
   * 测试AI模型
   */
  async testModel(data, db, cloud, admin) {
    const { id, testPrompt = '你好，请回复一段简短的测试文字。' } = data

    try {
      // 这里应该调用实际的AI接口进行测试
      // 由于豆包API配置复杂，这里先返回模拟结果

      const testResult = {
        success: true,
        response: '这是AI模型的测试回复。模型工作正常！',
        responseTime: Math.floor(Math.random() * 1000) + 500, // 模拟响应时间
        tokens: {
          prompt: testPrompt.length,
          completion: 15,
          total: testPrompt.length + 15
        },
        timestamp: new Date().toISOString()
      }

      // 记录测试日志
      await db.collection('ai_generation_logs').add({
        data: {
          modelId: id,
          testPrompt,
          result: testResult,
          adminId: admin._id,
          createTime: db.serverDate(),
          createTimestamp: Date.now()
        }
      })

      return testResult
    } catch (error) {
      const errorResult = {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }

      // 记录错误日志
      await db.collection('ai_error_logs').add({
        data: {
          modelId: id,
          testPrompt,
          result: errorResult,
          adminId: admin._id,
          createTime: db.serverDate(),
          createTimestamp: Date.now()
        }
      })

      throw error
    }
  },

  /**
   * 保存AI系统配置
   */
  async saveConfig(data, db, cloud, admin) {
    try {
      const {
        type = 'ai_config',
        status = 'active',
        model,
        provider,
        apiKey,
        apiUrl,
        temperature,
        maxTokens,
        topP,
        frequencyPenalty,
        presencePenalty,
        enableStream,
        enableCache,
        timeout,
        inputPrice,
        outputPrice
      } = data

      if (!model || !provider || !apiKey) {
        throw new Error('模型、提供商和API密钥不能为空')
      }

      // 先将现有的活跃配置设为非活跃
      await db.collection('system_config').where({
        type: 'ai_config',
        status: 'active'
      }).update({
        data: {
          status: 'inactive',
          updateTime: db.serverDate(),
          updateTimestamp: Date.now()
        }
      })

      // 创建新的AI配置
      const configData = {
        type,
        status,
        model,
        provider,
        apiKey,
        apiUrl,
        temperature: temperature || 0.7,
        maxTokens: maxTokens || 2000,
        topP: topP || 0.9,
        frequencyPenalty: frequencyPenalty || 0,
        presencePenalty: presencePenalty || 0,
        enableStream: enableStream || false,
        enableCache: enableCache || true,
        timeout: timeout || 30,
        inputPrice: inputPrice || 0, // 输入定价
        outputPrice: outputPrice || 0, // 输出定价
        createTime: db.serverDate(),
        updateTime: db.serverDate(),
        createTimestamp: Date.now(),
        updateTimestamp: Date.now(),
        createdBy: admin._id,
        updatedBy: admin._id
      }

      const result = await db.collection('system_config').add({
        data: configData
      })

      return {
        id: result._id,
        message: 'AI配置保存成功，新配置已生效',
        config: configData
      }
    } catch (error) {
      console.error('保存AI配置失败:', error)
      throw new Error(`保存AI配置失败: ${error.message}`)
    }
  },

  /**
   * 获取当前AI配置
   */
  async getConfig(data, db, cloud, admin) {
    try {
      const result = await db.collection('system_config')
        .where({
          type: 'ai_config',
          status: 'active'
        })
        .orderBy('updateTime', 'desc')
        .limit(1)
        .get()

      if (result.data && result.data.length > 0) {
        const config = result.data[0]
        // 隐藏API密钥的完整值
        if (config.apiKey) {
          const keyLength = config.apiKey.length
          config.apiKey = config.apiKey.substring(0, 4) + '*'.repeat(Math.max(0, keyLength - 8)) + config.apiKey.substring(Math.max(4, keyLength - 4))
        }

        return config
      } else {
        return null
      }
    } catch (error) {
      console.error('获取AI配置失败:', error)
      throw new Error(`获取AI配置失败: ${error.message}`)
    }
  },

  /**
   * 获取提示词模板（统一格式）
   */
  async getTemplates(data, db, cloud, admin) {
    try {
      const templatesResult = await db.collection('prompt_templates').get()
      
      // 如果没有模板，初始化默认模板
      if (templatesResult.data.length === 0) {
        const defaultTemplates = await this.initializeDefaultTemplates(db)
        return defaultTemplates
      }
      
      // 转换为统一格式
      return templatesResult.data.map(template => this.convertToUnifiedFormat(template))
      
    } catch (error) {
      console.error('获取模板失败:', error)
      throw new Error(`模板获取失败: ${error.message}`)
    }
  },

  /**
   * 初始化默认模板数据
   */
  async initializeDefaultTemplates(db) {
    const defaultTemplates = [
      {
        id: 'warm_v1',
        name: '🤗 温暖亲切',
        type: 'warm',
        category: 'term',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份温馨亲切、充满关怀的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"亲切问候 + 优点赞扬 + 温馨建议 + 暖心祝福"的结构。
2. **内容要求**：
   - **优点赞扬部分**：必须从素材中提炼2-3个最突出的优点，用温暖的语言表达赞扬，让学生感受到被认可。
   - **温馨建议部分**：如果素材中有需要改进的地方，请用关爱、鼓励的语气提出建议。如果没有，则给予温暖的期望和鼓励。
3. **语气风格**：语言要温馨、亲切、充满爱意，像慈母般的关怀，让学生感受到老师的温暖和关爱，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空时，请基于该学生的基本信息（姓名、班级等）生成一份积极正面的通用评语，重点关注学习态度、成长潜力和未来期望，避免具体事例描述。

请直接生成完整的评语内容，100-150字之间。`,
        variables: ['studentName', 'performanceMaterial'],
        metadata: {
          description: '语气温和，拉近师生距离，充满关爱的评语',
          emoji: '🤗',
          usageCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        version: 1,
        enabled: true,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'formal_v1',
        name: '📋 正式规范',
        type: 'formal',
        category: 'term',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份正式规范、客观专业的学期综合评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：优点详述部分必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑
3. **语气风格**：语言要真诚、平实、温暖，体现中职老师的专业性和人文关怀
4. **个性化**：必须尊重、保留且紧密结合提供的素材，体现学生独特性
5. **缺失处理**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '全面、客观、个性化的综合评价模板',
          tags: ['formal', 'comprehensive', 'term'],
          usageCount: 0
        },
        version: 1,
        enabled: true
      },
      {
        id: 'warm_v1',
        name: '优点鼓励型',
        type: 'warm',
        category: 'term',
        content: `你是一位善于发现学生闪光点的温暖班主任。你的任务是根据学生日常表现素材，撰写一份重点突出优点、充满鼓励的正向评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **聚焦优点**：从素材中挖掘至少3个具体优点，用鼓励性语言表达
2. **具体事例**：每个优点都要有具体的行为事例支撑，让表扬有据可依
3. **情感温度**：语言要充满温暖和欣赏，让学生感受到被肯定的力量
4. **未来导向**：在肯定现有表现的基础上，表达对学生未来发展的信心
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成80-120字的鼓励型评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '重点突出优点、充满鼓励的正向评语模板',
          tags: ['warm', 'encouraging', 'positive'],
          usageCount: 0
        },
        version: 1,
        enabled: true
      },
      {
        id: 'encouraging_v1',
        name: '改进建议型',
        type: 'encouraging',
        category: 'term',
        content: `你是一位经验丰富的教育导师，擅长以建设性方式指导学生成长。请根据学生表现素材，撰写一份侧重改进建议的发展性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **平衡表述**：先肯定1-2个优点，再委婉指出需要改进的方面
2. **具体建议**：针对改进点提供具体、可操作的建议和方法
3. **鼓励语调**：用"希望看到"、"相信你能"等积极语言表达期望
4. **成长导向**：将改进点表述为成长机会，而非批评
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成100-140字的发展性评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '侧重改进建议的发展性评语模板',
          tags: ['encouraging', 'developmental', 'constructive'],
          usageCount: 0
        },
        version: 1,
        enabled: true
      },
      {
        id: 'detailed_v1',
        name: '成长记录型',
        type: 'detailed',
        category: 'term',
        content: `你是一位注重记录学生成长轨迹的细心班主任。请根据学生表现素材，撰写一份侧重成长变化的阶段性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **时间维度**：体现学生在本学期的变化和进步轨迹
2. **对比描述**：如果素材中有前后对比，要突出变化过程
3. **具体记录**：引用具体的时间节点和事件，增强真实感
4. **成长价值**：强调每一个小进步的意义和价值
5. **未来展望**：基于当前进步趋势，对下阶段发展给出期待
6. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成110-160字的成长记录型评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '侧重成长变化的阶段性评语模板',
          tags: ['detailed', 'growth', 'progressive'],
          usageCount: 0
        },
        version: 1,
        enabled: true
      }
    ]

    // 批量插入默认模板
    const insertPromises = defaultTemplates.map(async (template) => {
      const unifiedTemplate = this.convertToUnifiedFormat({
        ...template,
        checksum: generateChecksum(template.content),
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      })

      return await db.collection('prompt_templates').add({
        data: unifiedTemplate
      })
    })

    await Promise.all(insertPromises)
    
    return defaultTemplates.map(template => this.convertToUnifiedFormat({
      ...template,
      _id: template.id
    }))
  },

  /**
   * 转换为统一模板格式
   */
  convertToUnifiedFormat(template) {
    console.log(`转换模板格式: ${template.name}, content长度: ${template.content?.length || 0}`)
    
    if (!template.content) {
      console.warn(`警告：模板 ${template.name} 的 content 字段为空`)
    }
    
    return {
      id: template._id || template.id,
      name: template.name,
      type: template.type,
      category: template.category || 'term',
      content: template.content || '', // 确保始终返回字符串，而不是使用不存在的 template.template
      variables: this.processTemplateVariables(template.variables),
      metadata: template.metadata || {
        author: 'admin',
        description: template.description || '',
        tags: [template.type],
        usageCount: 0
      },
      version: template.version || 1,
      checksum: template.checksum || generateChecksum(template.content || ''),
      enabled: template.enabled !== false,
      timestamps: {
        createdAt: template.timestamps?.createdAt || template.createdAt || new Date().toISOString(),
        updatedAt: template.timestamps?.updatedAt || template.updatedAt || new Date().toISOString()
      }
    }
  },

  /**
   * 处理模板变量格式
   */
  processTemplateVariables(variables) {
    if (!variables || !Array.isArray(variables)) {
      return []
    }

    return variables.map(variable => {
      if (typeof variable === 'string') {
        return {
          name: variable,
          type: 'string',
          required: true,
          description: this.getVariableDescription(variable),
          placeholder: `请输入${variable}`
        }
      }
      return variable
    })
  },

  /**
   * 获取变量描述
   */
  getVariableDescription(varName) {
    const descriptions = {
      'studentName': '学生姓名',
      'performanceMaterial': '学生表现材料',
      'className': '班级名称',
      'subjectName': '科目名称',
      'timeRange': '时间范围'
    }
    return descriptions[varName] || `${varName}参数`
  },

  /**
   * 更新提示词模板（支持版本控制）
   */
  async updateTemplate(data, db, cloud, admin) {
    const { id, name, type, content, variables, metadata, enabled, changeLog = '模板更新' } = data
    
    if (!id) {
      throw new Error('模板ID不能为空')
    }

    try {
      // 获取当前模板
      const currentTemplateResult = await db.collection('prompt_templates').doc(id).get()
      if (!currentTemplateResult.data) {
        throw new Error('模板不存在')
      }

      const currentTemplate = this.convertToUnifiedFormat(currentTemplateResult.data)
      
      // 准备更新数据
      const updates = {}
      if (name) updates.name = name
      if (type) updates.type = type
      if (content) updates.content = content
      if (variables) updates.variables = this.processTemplateVariables(variables)
      if (metadata) updates.metadata = { ...currentTemplate.metadata, ...metadata }
      if (enabled !== undefined) updates.enabled = enabled

      // 使用安全更新工具
      const { updatedTemplate, backup } = safeTemplateUpdate(currentTemplate, updates)
      
      // 保存版本备份（暂时跳过，避免集合不存在错误）
      try {
        await db.collection('template_versions').add({
          data: {
            ...backup,
            changeLog,
            createdBy: admin._id,
            createTime: db.serverDate()
          }
        })
      } catch (error) {
        console.warn('版本备份失败，继续执行:', error.message)
        // 不抛出错误，允许模板更新继续进行
      }

      // 更新主模板
      await db.collection('prompt_templates').doc(id).update({
        data: {
          ...updatedTemplate,
          updateTime: db.serverDate(),
          updateTimestamp: Date.now(),
          updatedBy: admin._id
        }
      })

      // 记录使用统计
      await this.updateTemplateUsageStats(id, 'update', db)

      return {
        message: '提示词模板更新成功',
        template: updatedTemplate,
        version: updatedTemplate.version
      }
      
    } catch (error) {
      console.error('模板更新失败:', error)
      throw new Error(`模板更新失败: ${error.message}`)
    }
  },

  /**
   * 创建新模板
   */
  async createTemplate(data, db, cloud, admin) {
    const { name, type, category = 'term', content, variables, metadata } = data
    
    if (!name || !type || !content) {
      throw new Error('模板名称、类型和内容不能为空')
    }

    try {
      // 验证模板内容
      const templateData = {
        id: `${type}_custom_${Date.now()}`,
        name,
        type,
        category,
        content,
        variables: this.processTemplateVariables(variables || []),
        metadata: {
          author: admin.name || 'admin',
          description: metadata?.description || `自定义${type}模板`,
          tags: metadata?.tags || [type, 'custom'],
          usageCount: 0,
          ...metadata
        },
        version: 1,
        checksum: generateChecksum(content),
        enabled: true,
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }

      // 验证模板数据
      validateTemplate(templateData)

      // 插入数据到数据库
      const result = await db.collection('prompt_templates').add({
        data: {
          ...templateData,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          createTimestamp: Date.now(),
          updateTimestamp: Date.now(),
          createdBy: admin._id
        }
      })

      return {
        message: '模板创建成功',
        template: { ...templateData, id: result._id },
        id: result._id
      }
      
    } catch (error) {
      console.error('模板创建失败:', error)
      throw new Error(`模板创建失败: ${error.message}`)
    }
  },

  /**
   * 按类型获取单个模板（供小程序使用）
   */
  async getTemplateByType(data, db, cloud, admin) {
    const { type, enabled = true } = data
    
    if (!type) {
      throw new Error('模板类型不能为空')
    }

    try {
      let query = db.collection('prompt_templates').where({
        type: type
      })

      if (enabled) {
        query = query.where({
          enabled: true
        })
      }

      const templatesResult = await query.orderBy('version', 'desc').limit(1).get()
      
      if (templatesResult.data.length === 0) {
        // 如果没有找到指定类型的模板，返回默认模板
        const defaultTemplate = await this.createDefaultTemplateForType(type, db)
        return this.convertToUnifiedFormat(defaultTemplate)
      }

      const template = this.convertToUnifiedFormat(templatesResult.data[0])
      
      // 记录使用统计
      await this.updateTemplateUsageStats(template.id, 'use', db)
      
      return template
      
    } catch (error) {
      console.error('获取模板失败:', error)
      throw new Error(`获取${type}模板失败: ${error.message}`)
    }
  },

  /**
   * 为指定类型创建默认模板
   */
  async createDefaultTemplateForType(type, db) {
    const defaultTemplates = {
      'formal': {
        id: 'formal_v1',
        name: '综合评价型',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的资深班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：优点详述部分必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑
3. **语气风格**：语言要真诚、平实、温暖，体现中职老师的专业性和人文关怀
4. **个性化**：必须尊重、保留且紧密结合提供的素材，体现学生独特性
5. **缺失处理**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`
      },
      'warm': {
        id: 'warm_v1',
        name: '优点鼓励型',
        content: `你是一位善于发现学生闪光点的温暖班主任。你的任务是根据学生日常表现素材，撰写一份重点突出优点、充满鼓励的正向评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **聚焦优点**：从素材中挖掘至少3个具体优点，用鼓励性语言表达
2. **具体事例**：每个优点都要有具体的行为事例支撑，让表扬有据可依
3. **情感温度**：语言要充满温暖和欣赏，让学生感受到被肯定的力量
4. **未来导向**：在肯定现有表现的基础上，表达对学生未来发展的信心
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成80-120字的鼓励型评语。`
      },
      'encouraging': {
        id: 'encouraging_v1',
        name: '改进建议型',
        content: `你是一位经验丰富的教育导师，擅长以建设性方式指导学生成长。请根据学生表现素材，撰写一份侧重改进建议的发展性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **平衡表述**：先肯定1-2个优点，再委婉指出需要改进的方面
2. **具体建议**：针对改进点提供具体、可操作的建议和方法
3. **鼓励语调**：用"希望看到"、"相信你能"等积极语言表达期望
4. **成长导向**：将改进点表述为成长机会，而非批评
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成100-140字的发展性评语。`
      },
      'detailed': {
        id: 'detailed_v1',
        name: '成长记录型',
        content: `你是一位注重记录学生成长轨迹的细心班主任。请根据学生表现素材，撰写一份侧重成长变化的阶段性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **时间维度**：体现学生在本学期的变化和进步轨迹
2. **对比描述**：如果素材中有前后对比，要突出变化过程
3. **具体记录**：引用具体的时间节点和事件，增强真实感
4. **成长价值**：强调每一个小进步的意义和价值
5. **未来展望**：基于当前进步趋势，对下阶段发展给出期待
6. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成110-160字的成长记录型评语。`
      }
    }

    const defaultTemplate = defaultTemplates[type] || defaultTemplates['warm']
    
    const templateData = {
      ...defaultTemplate,
      type,
      category: 'term',
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: `默认${type}模板`,
        tags: [type, 'default'],
        usageCount: 0
      },
      version: 1,
      checksum: generateChecksum(defaultTemplate.content),
      enabled: true,
      timestamps: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    }

    const result = await db.collection('prompt_templates').add({
      data: templateData
    })

    return { ...templateData, _id: result._id }
  },

  /**
   * 更新模板使用统计
   */
  async updateTemplateUsageStats(templateId, action, db) {
    try {
      if (action === 'use') {
        await db.collection('prompt_templates').doc(templateId).update({
          data: {
            'metadata.usageCount': db.command.inc(1),
            'metadata.lastUsedAt': new Date().toISOString(),
            'timestamps.lastUsedAt': new Date().toISOString()
          }
        })
      }
    } catch (error) {
      console.warn('更新模板使用统计失败:', error)
    }
  },

  /**
   * 获取AI使用统计
   */
  async getStatistics(data, db, cloud, admin) {
    const { params = {} } = data
    const { timeRange = '30d' } = params
    
    // 计算时间范围
    const now = new Date()
    let startTime
    
    switch (timeRange) {
      case '1d':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
      default:
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
    }
    
    // 获取评语生成统计
    const commentsResult = await db.collection('comments').where({
      createTime: db.command.gte(startTime)
    }).get()
    
    // 获取AI错误日志统计
    const errorLogsResult = await db.collection('ai_error_logs').where({
      createTime: db.command.gte(startTime)
    }).get()
    
    // 计算统计数据
    const totalCalls = commentsResult.data.length
    const successCalls = commentsResult.data.length
    const failedCalls = errorLogsResult.data.length
    
    // 风格分布统计
    const styleDistribution = commentsResult.data.reduce((acc, comment) => {
      const style = comment.style || 'warm'
      acc[style] = (acc[style] || 0) + 1
      return acc
    }, {})
    
    return {
      totalModels: 1, // 当前主要使用豆包
      activeModels: 1,
      usage: {
        totalCalls,
        successCalls,
        failedCalls,
        successRate: totalCalls > 0 ? ((successCalls / totalCalls) * 100).toFixed(2) : 0,
        totalTokens: commentsResult.data.reduce((sum, comment) => {
          return sum + (comment.content?.length || 0) * 1.5 // 估算token数
        }, 0)
      },
      styleDistribution,
      timeRange,
      generatedAt: new Date().toISOString()
    }
  },

  /**
   * 获取模型性能分析
   */
  async getModelPerformance(data, db, cloud, admin) {
    const { modelId } = data
    
    // 获取最近30天的测试日志
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    let query = db.collection('ai_generation_logs').where({
      createTime: db.command.gte(thirtyDaysAgo)
    })
    
    if (modelId) {
      query = query.where({ modelId })
    }
    
    const logsResult = await query.get()
    
    // 分析性能数据
    const logs = logsResult.data
    const successLogs = logs.filter(log => log.result.success)
    const failedLogs = logs.filter(log => !log.result.success)
    
    const avgResponseTime = successLogs.length > 0 
      ? successLogs.reduce((sum, log) => sum + (log.result.responseTime || 0), 0) / successLogs.length
      : 0
    
    return {
      totalTests: logs.length,
      successfulTests: successLogs.length,
      failedTests: failedLogs.length,
      successRate: logs.length > 0 ? ((successLogs.length / logs.length) * 100).toFixed(2) : 0,
      averageResponseTime: Math.round(avgResponseTime),
      performanceTrend: 'stable', // 简化处理，可以后续增加趋势分析
      lastAnalyzedAt: new Date().toISOString()
    }
  }
}