@echo off
chcp 65001 > nul
echo 🚀 部署最新版本的adminAPI云函数...
echo.

cd /d "%~dp0"

echo 📁 当前目录: %cd%
echo.

echo 🔍 检查云函数目录...
if not exist "cloudfunctions\adminAPI" (
    echo ❌ 云函数目录不存在: cloudfunctions\adminAPI
    pause
    exit /b 1
)

echo 📦 切换到云函数目录...
cd cloudfunctions\adminAPI

echo 🔧 安装依赖...
call npm install
if errorlevel 1 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)

echo 🚀 部署云函数...
call tcb fn deploy adminAPI --dir . --force
if errorlevel 1 (
    echo ❌ 部署失败
    pause
    exit /b 1
)

echo.
echo ✅ adminAPI云函数部署完成！
echo 💡 现在你可以在管理后台中测试AI配置的定价字段功能
echo.
pause