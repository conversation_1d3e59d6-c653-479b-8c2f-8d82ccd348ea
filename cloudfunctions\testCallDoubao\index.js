const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

exports.main = async (event, context) => {
  console.log('🎯 testCallDoubao云函数接收到调用，参数:', event);
  
  // 简单返回成功，用于测试
  return {
    success: true,
    data: {
      content: `测试生成的评语：${event.studentName || '该同学'}在本学期表现良好，建议继续努力。（风格：${event.style || 'warm'}）`,
      testInfo: {
        receivedParams: Object.keys(event),
        hasStudentName: !!event.studentName,
        hasPerformanceMaterial: !!event.performanceMaterial,
        style: event.style
      }
    },
    message: '测试云函数调用成功'
  };
};