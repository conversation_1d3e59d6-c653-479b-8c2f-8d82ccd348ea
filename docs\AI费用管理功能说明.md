# AI费用管理功能说明

## 功能概述

本系统新增了完整的AI费用管理功能，支持不同AI模型的定价配置和费用统计，帮助管理员更好地控制和监控AI使用成本。

## 主要功能

### 1. 模型定价配置

在AI配置页面中，管理员可以为每个AI模型配置：
- **输入定价**：输入tokens的费用，单位为元/千tokens
- **输出定价**：输出tokens的费用，单位为元/千tokens

#### 默认定价标准
- **豆包Flash模型** (`doubao-seed-1-6-flash-250715`)
  - 输入定价：0.00007元/千tokens
  - 输出定价：0.0003元/千tokens

- **豆包Pro模型** (`doubao-pro-32k`)
  - 输入定价：0.00075元/千tokens
  - 输出定价：0.0030元/千tokens

### 2. 实时费用计算

系统在每次AI调用时会自动：
- 根据输入输出tokens数量计算实际费用
- 记录详细的费用信息到数据库
- 支持不同模型的动态定价

#### 费用计算公式
```
输入费用 = (输入tokens数量 / 1000) × 输入定价
输出费用 = (输出tokens数量 / 1000) × 输出定价
总费用 = 输入费用 + 输出费用
```

### 3. 费用统计展示

#### AI Tokens统计页面
- 总费用统计
- 今日费用统计
- 平均每次费用
- 平均每千tokens费用
- 今日调用次数

#### 教师使用统计页面
- 每个教师的总费用
- 平均每次调用费用
- 费用排名
- 费用分布统计

### 4. 数据库结构

#### ai_usage集合新增字段
```javascript
{
  // 原有字段...
  inputTokens: Number,        // 输入tokens数量
  outputTokens: Number,       // 输出tokens数量
  cost: Number,              // 总费用（元）
  inputCost: Number,         // 输入费用（元）
  outputCost: Number,        // 输出费用（元）
  modelInputPrice: Number,   // 模型输入定价
  modelOutputPrice: Number   // 模型输出定价
}
```

#### ai_configs集合新增字段
```javascript
{
  // 原有字段...
  inputPrice: Number,   // 输入定价（元/千tokens）
  outputPrice: Number,  // 输出定价（元/千tokens）
  totalCost: Number     // 总费用统计
}
```

#### system_config集合新增字段
```javascript
{
  // 原有字段...
  inputPrice: Number,   // 输入定价（元/千tokens）
  outputPrice: Number   // 输出定价（元/千tokens）
}
```

## 使用指南

### 1. 配置模型定价

1. 进入管理后台的"AI配置"页面
2. 点击"添加模型"或编辑现有模型
3. 在表单中填入：
   - 模型名称
   - 提供商
   - 模型版本
   - API密钥和基础URL
   - **输入定价**（元/千tokens）
   - **输出定价**（元/千tokens）
4. 保存配置

### 2. 查看费用统计

#### 总体统计
- 访问Dashboard页面查看AI Tokens消耗与费用统计
- 查看总费用、今日费用等关键指标

#### 教师费用统计
- 访问教师AI使用与费用统计页面
- 查看每个教师的费用消耗排名
- 分析费用分布和使用模式

### 3. 费用监控

系统提供多维度的费用监控：
- **实时监控**：每次AI调用都会实时计算和记录费用
- **历史统计**：支持查看历史费用趋势
- **用户分析**：分析不同教师的费用消耗模式
- **模型对比**：对比不同AI模型的费用效率

## 技术实现

### 1. 费用计算服务
- `services/costCalculationService.js`：核心费用计算逻辑
- 支持批量计算、内容估算等功能
- 高精度浮点数处理，避免精度丢失

### 2. 云函数支持
- `cloudfunctions/getCostStats`：费用统计云函数
- 提供多种统计维度的API接口
- 支持实时查询和历史分析

### 3. 前端展示
- 集成到现有的统计页面中
- 响应式设计，支持多种设备
- 实时数据更新

## 注意事项

1. **精度处理**：费用计算保留5位小数，确保精度
2. **兼容性**：保持与现有数据结构的兼容性
3. **性能优化**：批量计算和缓存机制提升性能
4. **错误处理**：完善的错误处理和降级方案

## 测试验证

系统包含完整的测试用例：
- 基础费用计算测试
- 内容估算测试
- 批量计算测试
- 边界条件测试

运行测试：
```bash
node test/costCalculationTest.js
```

## 未来扩展

1. **费用预警**：设置费用阈值和预警机制
2. **费用报表**：生成详细的费用分析报表
3. **预算管理**：支持预算设置和控制
4. **成本优化**：AI模型成本效率分析和建议
