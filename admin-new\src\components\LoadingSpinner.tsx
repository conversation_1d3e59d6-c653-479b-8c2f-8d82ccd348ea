import React from 'react'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  spinning?: boolean
  children?: React.ReactNode
  className?: string
  overlay?: boolean
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip,
  spinning = true,
  children,
  className = '',
  overlay = false
}) => {
  // 自定义加载图标
  const customIcon = <LoadingOutlined style={{ fontSize: getSizeValue(size) }} spin />

  // 如果有children，使用Spin包装
  if (children) {
    return (
      <Spin 
        indicator={customIcon}
        spinning={spinning}
        tip={tip}
        size={size}
        className={className}
      >
        {children}
      </Spin>
    )
  }

  // 全屏加载遮罩
  if (overlay) {
    return (
      <div className={`fixed inset-0 bg-white bg-opacity-80 backdrop-blur-sm z-50 flex-center ${className}`}>
        <div className="text-center">
          <Spin 
            indicator={customIcon}
            size={size}
            tip={tip}
          />
        </div>
      </div>
    )
  }

  // 普通加载器
  return (
    <div className={`flex-center ${className}`}>
      <Spin 
        indicator={customIcon}
        size={size}
        tip={tip}
      />
    </div>
  )
}

// 获取尺寸值
const getSizeValue = (size: 'small' | 'default' | 'large'): number => {
  const sizeMap = {
    small: 16,
    default: 24,
    large: 32
  }
  return sizeMap[size]
}

// 页面级加载组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = "加载中..." }) => (
  <div className="min-h-[400px] flex-center">
    <div className="text-center">
      <div className="loading-spinner mb-4"></div>
      <p className="text-gray-500 dark:text-gray-300 text-sm">{tip}</p>
    </div>
  </div>
)

// 内容区加载组件
export const ContentLoading: React.FC<{ tip?: string; height?: string | number }> = ({ 
  tip = "加载中...", 
  height = 200 
}) => (
  <div 
    className="flex-center bg-gray-50 rounded-lg"
    style={{ height: typeof height === 'number' ? `${height}px` : height }}
  >
    <div className="text-center">
      <div className="loading-spinner mb-2"></div>
      <p className="text-gray-400 dark:text-gray-300 text-xs">{tip}</p>
    </div>
  </div>
)

// 卡片加载组件
export const CardLoading: React.FC = () => (
  <div className="card">
    <div className="card-body">
      <div className="animate-pulse space-y-4">
        <div className="flex items-center space-x-3">
          <div className="loading-skeleton-avatar"></div>
          <div className="flex-1">
            <div className="loading-skeleton-title w-32"></div>
            <div className="loading-skeleton-text w-24"></div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="loading-skeleton-text"></div>
          <div className="loading-skeleton-text w-4/5"></div>
          <div className="loading-skeleton-text w-3/5"></div>
        </div>
      </div>
    </div>
  </div>
)

// 表格加载组件
export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-4">
    {/* 表头 */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, index) => (
        <div key={index} className="loading-skeleton-text h-5"></div>
      ))}
    </div>
    
    {/* 表格行 */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div key={colIndex} className="loading-skeleton-text h-4"></div>
        ))}
      </div>
    ))}
  </div>
)

// 列表加载组件
export const ListLoading: React.FC<{ items?: number }> = ({ items = 6 }) => (
  <div className="space-y-4">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg">
        <div className="loading-skeleton-avatar"></div>
        <div className="flex-1">
          <div className="loading-skeleton-title w-48 mb-2"></div>
          <div className="loading-skeleton-text w-32"></div>
        </div>
        <div className="loading-skeleton w-16 h-8 rounded"></div>
      </div>
    ))}
  </div>
)

// 图表加载组件
export const ChartLoading: React.FC<{ height?: number }> = ({ height = 300 }) => (
  <div 
    className="loading-skeleton rounded-lg"
    style={{ height: `${height}px` }}
  >
    <div className="flex items-end justify-around h-full p-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <div
          key={index}
          className="bg-gray-300 rounded-t"
          style={{
            width: '20px',
            height: `${Math.random() * 60 + 20}%`,
            animation: `pulse 2s infinite ${index * 0.1}s`
          }}
        />
      ))}
    </div>
  </div>
)

// 按钮加载组件
export const ButtonLoading: React.FC<{
  loading?: boolean
  children: React.ReactNode
  [key: string]: any
}> = ({ loading = false, children, ...props }) => (
  <button {...props} disabled={loading || props.disabled}>
    {loading && <div className="loading-spinner mr-2" style={{ width: 14, height: 14 }}></div>}
    {children}
  </button>
)

// 文本加载组件
export const TextLoading: React.FC<{ 
  lines?: number 
  width?: string[]
  className?: string 
}> = ({ 
  lines = 3, 
  width = ['100%', '80%', '60%'],
  className = ''
}) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <div
        key={index}
        className="loading-skeleton-text"
        style={{ width: width[index] || width[width.length - 1] }}
      />
    ))}
  </div>
)

// 头像加载组件
export const AvatarLoading: React.FC<{ size?: number }> = ({ size = 40 }) => (
  <div 
    className="loading-skeleton-avatar"
    style={{ width: size, height: size }}
  />
)

// 图片加载组件
export const ImageLoading: React.FC<{ 
  width?: number | string
  height?: number | string
  className?: string
}> = ({ 
  width = '100%', 
  height = 200,
  className = '' 
}) => (
  <div 
    className={`loading-skeleton ${className}`}
    style={{ 
      width: typeof width === 'number' ? `${width}px` : width,
      height: typeof height === 'number' ? `${height}px` : height
    }}
  />
)

export default LoadingSpinner