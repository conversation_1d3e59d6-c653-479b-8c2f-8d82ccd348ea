const cloudbase = require('@cloudbase/node-sdk')

async function checkCostData() {
  const app = cloudbase.init({
    env: 'development-s6a2g',
    secretId: 'AKIDxxxxxxxx',
    secretKey: 'xxxxxxxx',
  })

  const db = app.database()

  try {
    // 检查ai_usage记录
    console.log('🔍 检查ai_usage集合...')
    const usageRecords = await db.collection('ai_usage').get()
    console.log(`📊 ai_usage记录数: ${usageRecords.data.length}`)
    
    if (usageRecords.data.length > 0) {
      console.log('💰 前5条费用记录:', usageRecords.data.slice(0, 5).map(item => ({
        teacherId: item.teacherId,
        cost: item.cost,
        tokensUsed: item.tokensUsed,
        createTime: item.createTime
      })))
    }

    // 检查ai_configs配置
    console.log('\n🔍 检查ai_configs集合...')
    const aiConfigs = await db.collection('ai_configs').get()
    console.log(`📊 ai_configs记录数: ${aiConfigs.data.length}`)
    
    if (aiConfigs.data.length > 0) {
      console.log('⚙️  AI配置:', aiConfigs.data.map(item => ({
        name: item.name,
        inputPrice: item.inputPrice,
        outputPrice: item.outputPrice,
        model: item.model
      })))
    }

    // 检查comments是否有tokens记录
    console.log('\n🔍 检查comments集合...')
    const comments = await db.collection('comments').get()
    console.log(`📊 评语总数: ${comments.data.length}`)
    
    if (comments.data.length > 0) {
      console.log('💬 前3条tokens数据:', comments.data.slice(0, 3).map(item => ({
        teacherId: item.teacherId,
        tokensUsed: item.tokensUsed,
        contentLength: item.content?.length || 0
      })))
    }

    // 获取费用汇总
    console.log('\n💰 费用统计汇总...')
    const result = await db.collection('ai_usage')
      .aggregate()
      .group({
        _id: null,
        totalCost: _.sum('$cost'),
        totalTokens: _.sum('$tokensUsed'),
        totalCalls: _.sum(1),
        avgCostPerCall: _.avg('$cost')
      })
      .end()

    console.log('📊 总费用统计:', result.list[0])

  } catch (error) {
    console.error('❌ 检查失败:', error)
  }
}

checkCostData()