# 部署getCostStats云函数指南

## 问题描述

前端调用`getCostStats`云函数时出现错误：
```
[FUNCTION_NOT_FOUND] FunctionName parameter could not be found
```

这表明`getCostStats`云函数没有部署到云端。

## 解决方案

### 方案1：部署getCostStats云函数（推荐）

#### 步骤1：检查云函数代码
确认`cloudfunctions/getCostStats/`目录下有以下文件：
- `index.js` - 云函数主代码
- `package.json` - 依赖配置

#### 步骤2：部署云函数

**使用微信开发者工具：**
1. 打开微信开发者工具
2. 在项目中找到`cloudfunctions/getCostStats`文件夹
3. 右键点击该文件夹
4. 选择"上传并部署：云端安装依赖"
5. 等待部署完成

**使用命令行（如果配置了cloudbase cli）：**
```bash
# 进入云函数目录
cd cloudfunctions/getCostStats

# 安装依赖
npm install

# 部署云函数
tcb fn deploy getCostStats
```

#### 步骤3：验证部署
1. 在微信开发者工具的云开发控制台中
2. 进入"云函数"页面
3. 确认`getCostStats`函数出现在列表中
4. 状态显示为"正常"

### 方案2：使用现有的adminAPI代理（临时方案）

如果无法部署`getCostStats`云函数，可以确保`adminAPI`云函数正常工作：

#### 检查adminAPI云函数
1. 确认`adminAPI`云函数已部署
2. 检查`adminAPI/index.js`中的cost模块代理逻辑：

```javascript
case 'cost':
  // 费用统计模块，代理调用getCostStats云函数
  admin = await checkPermission({ action, requestId, timestamp, ...requestData })
  try {
    const costResult = await cloud.callFunction({
      name: 'getCostStats',
      data: {
        action: method === 'getAllStats' ? undefined : method,
        ...requestData
      }
    })
    result = costResult.result
  } catch (error) {
    console.error('调用getCostStats云函数失败:', error)
    result = { success: false, error: error.message }
  }
  break
```

### 方案3：前端计算备用方案（已实现）

前端代码已经实现了多重容错机制：
1. 优先尝试adminAPI代理
2. 其次尝试直接调用getCostStats
3. 最后使用前端CostCalculator实时计算

## 部署后验证

### 1. 测试云函数调用
在微信开发者工具的云函数控制台中测试：

```javascript
// 测试数据
{
  "action": "getAllCostStats"
}
```

期望返回：
```javascript
{
  "success": true,
  "data": {
    "total": { "totalCost": 0.001, "totalCalls": 10, ... },
    "daily": { "todayCost": 0.0001, "todayCalls": 2 },
    "teachers": [...],
    "models": [...],
    "trend": [...]
  }
}
```

### 2. 测试前端调用
1. 打开管理后台数据大屏
2. 查看浏览器控制台日志
3. 确认看到以下成功日志：
   ```
   📊 通过adminAPI代理结果: {success: true, data: {...}}
   ✅ 费用统计数据设置成功: {...}
   ```

### 3. 验证费用显示
确认以下数据正常显示：
- AI Tokens消耗与费用统计卡片中的总费用、今日费用等
- 教师AI使用与费用统计表格中每位教师的费用数据

## 常见问题

### Q1: 部署时提示权限错误
**A:** 确保微信开发者工具已登录，且账号有云函数部署权限

### Q2: 云函数部署成功但调用失败
**A:** 检查云函数日志，可能是数据库权限或集合不存在的问题

### Q3: 费用数据显示为0
**A:** 可能是ai_usage集合为空或费用字段未计算，运行以下脚本：
```bash
node sync-comments-to-ai-usage.js
node fix-cost-calculation.js
```

### Q4: adminAPI代理也失败
**A:** 检查adminAPI云函数是否正常部署，查看其调用日志

## 监控建议

1. **定期检查云函数状态**：确保getCostStats云函数正常运行
2. **监控调用日志**：关注云函数调用错误和性能
3. **数据完整性检查**：定期验证ai_usage集合的费用数据
4. **前端错误监控**：关注浏览器控制台的费用相关错误

## 技术说明

### 云函数架构
```
前端调用
├── adminAPI (代理层)
│   └── getCostStats (费用统计)
└── 前端CostCalculator (备用计算)

数据流
ai_usage集合 → getCostStats云函数 → adminAPI代理 → 前端显示
```

### 容错机制
1. **多重调用策略**：adminAPI代理 → 直接调用 → 前端计算
2. **错误日志记录**：详细的调用过程日志
3. **优雅降级**：云函数失败时自动使用前端计算

## 更新记录

- 2024-08-02：识别getCostStats云函数未部署问题
- 优化前端调用策略，增加容错机制
- 添加详细的部署和验证指南
