/**
 * AI费用计算测试
 * 验证费用计算功能的准确性
 */

// 直接导入费用计算服务的代码，避免路径问题
const CostCalculationService = class {
  constructor() {
    // 默认定价配置（元/千tokens）
    this.defaultPricing = {
      'doubao-seed-1-6-flash-250715': {
        inputPrice: 0.00007,   // 输入：0.00007元/千tokens
        outputPrice: 0.0003    // 输出：0.0003元/千tokens
      },
      'doubao-pro-32k': {
        inputPrice: 0.00075,   // 输入：0.00075元/千tokens
        outputPrice: 0.0030    // 输出：0.0030元/千tokens
      },
      'gpt-4-turbo': {
        inputPrice: 0.01,      // OpenAI定价示例
        outputPrice: 0.03
      }
    }
  }

  calculateCost({
    model,
    inputTokens = 0,
    outputTokens = 0,
    inputPrice = null,
    outputPrice = null
  }) {
    try {
      // 获取模型定价
      const pricing = this.getModelPricing(model, inputPrice, outputPrice)

      // 计算费用（定价是按千tokens计算）
      const inputCost = (inputTokens / 1000) * pricing.inputPrice
      const outputCost = (outputTokens / 1000) * pricing.outputPrice
      const totalCost = inputCost + outputCost

      return {
        success: true,
        data: {
          model,
          inputTokens,
          outputTokens,
          totalTokens: inputTokens + outputTokens,
          inputPrice: pricing.inputPrice,
          outputPrice: pricing.outputPrice,
          inputCost: Math.round(inputCost * 100000) / 100000, // 保留5位小数
          outputCost: Math.round(outputCost * 100000) / 100000,
          totalCost: Math.round(totalCost * 100000) / 100000,
          currency: 'CNY'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: {
          model,
          inputTokens,
          outputTokens,
          totalTokens: inputTokens + outputTokens,
          inputCost: 0,
          outputCost: 0,
          totalCost: 0
        }
      }
    }
  }

  getModelPricing(model, customInputPrice = null, customOutputPrice = null) {
    // 优先使用自定义定价
    if (customInputPrice !== null && customOutputPrice !== null) {
      return {
        inputPrice: customInputPrice,
        outputPrice: customOutputPrice
      }
    }

    // 使用默认定价
    const defaultPrice = this.defaultPricing[model]
    if (defaultPrice) {
      return defaultPrice
    }

    // 如果没有找到对应模型，使用通用定价
    return {
      inputPrice: 0.001,  // 默认输入定价
      outputPrice: 0.002  // 默认输出定价
    }
  }

  batchCalculateCost(usageRecords) {
    const results = []
    let totalCost = 0
    let totalInputTokens = 0
    let totalOutputTokens = 0

    for (const record of usageRecords) {
      const result = this.calculateCost(record)
      results.push(result)

      if (result.success) {
        totalCost += result.data.totalCost
        totalInputTokens += result.data.inputTokens
        totalOutputTokens += result.data.outputTokens
      }
    }

    return {
      success: true,
      data: {
        records: results,
        summary: {
          totalRecords: usageRecords.length,
          totalInputTokens,
          totalOutputTokens,
          totalTokens: totalInputTokens + totalOutputTokens,
          totalCost: Math.round(totalCost * 100000) / 100000
        }
      }
    }
  }

  estimateCostFromContent({
    content,
    model,
    inputOutputRatio = 1
  }) {
    // 简单的tokens估算：中文字符约1.5个token，英文单词约1个token
    const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (content.match(/[a-zA-Z]+/g) || []).length
    const estimatedTokens = Math.ceil(chineseChars * 1.5 + englishWords)

    // 按比例分配输入输出tokens
    const totalRatio = 1 + inputOutputRatio
    const inputTokens = Math.ceil(estimatedTokens / totalRatio)
    const outputTokens = Math.ceil(estimatedTokens * inputOutputRatio / totalRatio)

    return this.calculateCost({
      model,
      inputTokens,
      outputTokens
    })
  }
}

const costCalculationService = new CostCalculationService()

// 测试用例
const testCases = [
  {
    name: '豆包Flash模型 - 基础测试',
    input: {
      model: 'doubao-seed-1-6-flash-250715',
      inputTokens: 1000,
      outputTokens: 2000
    },
    expected: {
      inputCost: 0.00007, // 1000/1000 * 0.00007 = 0.00007
      outputCost: 0.0006, // 2000/1000 * 0.0003 = 0.0006
      totalCost: 0.00067
    }
  },
  {
    name: '豆包Pro模型 - 基础测试',
    input: {
      model: 'doubao-pro-32k',
      inputTokens: 1000,
      outputTokens: 1000
    },
    expected: {
      inputCost: 0.00075, // 1000/1000 * 0.00075 = 0.00075
      outputCost: 0.0030, // 1000/1000 * 0.0030 = 0.0030
      totalCost: 0.00375
    }
  },
  {
    name: '自定义定价测试',
    input: {
      model: 'custom-model',
      inputTokens: 500,
      outputTokens: 1500,
      inputPrice: 0.001,
      outputPrice: 0.002
    },
    expected: {
      inputCost: 0.0005, // 500/1000 * 0.001 = 0.0005
      outputCost: 0.003, // 1500/1000 * 0.002 = 0.003
      totalCost: 0.0035
    }
  },
  {
    name: '零tokens测试',
    input: {
      model: 'doubao-seed-1-6-flash-250715',
      inputTokens: 0,
      outputTokens: 0
    },
    expected: {
      inputCost: 0,
      outputCost: 0,
      totalCost: 0
    }
  },
  {
    name: '大数量tokens测试',
    input: {
      model: 'doubao-pro-32k',
      inputTokens: 100000,
      outputTokens: 50000
    },
    expected: {
      inputCost: 0.075, // 100000/1000 * 0.00075 = 0.075
      outputCost: 0.15, // 50000/1000 * 0.0030 = 0.15
      totalCost: 0.225
    }
  }
]

// 内容估算测试用例
const contentEstimationTests = [
  {
    name: '中文内容估算',
    input: {
      content: '这是一个测试评语，用于验证费用计算功能的准确性。',
      model: 'doubao-seed-1-6-flash-250715'
    },
    expectedTokensRange: [30, 50] // 预期tokens范围
  },
  {
    name: '英文内容估算',
    input: {
      content: 'This is a test comment for verifying the accuracy of cost calculation.',
      model: 'doubao-pro-32k'
    },
    expectedTokensRange: [10, 20]
  },
  {
    name: '混合内容估算',
    input: {
      content: 'Hello 你好，这是一个mixed content测试。',
      model: 'doubao-seed-1-6-flash-250715'
    },
    expectedTokensRange: [15, 25]
  }
]

// 批量计算测试用例
const batchCalculationTest = [
  {
    model: 'doubao-seed-1-6-flash-250715',
    inputTokens: 1000,
    outputTokens: 2000
  },
  {
    model: 'doubao-pro-32k',
    inputTokens: 500,
    outputTokens: 1500
  },
  {
    model: 'gpt-4-turbo',
    inputTokens: 800,
    outputTokens: 1200
  }
]

// 运行测试
function runTests() {
  console.log('🧪 开始AI费用计算测试...\n')
  
  let passedTests = 0
  let totalTests = 0
  
  // 基础费用计算测试
  console.log('📊 基础费用计算测试:')
  testCases.forEach((testCase, index) => {
    totalTests++
    console.log(`\n测试 ${index + 1}: ${testCase.name}`)
    
    const result = costCalculationService.calculateCost(testCase.input)
    
    if (result.success) {
      const { inputCost, outputCost, totalCost } = result.data
      const { expected } = testCase
      
      // 使用较小的误差范围进行比较（由于浮点数精度问题）
      const tolerance = 0.000001
      
      const inputMatch = Math.abs(inputCost - expected.inputCost) < tolerance
      const outputMatch = Math.abs(outputCost - expected.outputCost) < tolerance
      const totalMatch = Math.abs(totalCost - expected.totalCost) < tolerance
      
      if (inputMatch && outputMatch && totalMatch) {
        console.log('✅ 通过')
        console.log(`   输入费用: ${inputCost} (预期: ${expected.inputCost})`)
        console.log(`   输出费用: ${outputCost} (预期: ${expected.outputCost})`)
        console.log(`   总费用: ${totalCost} (预期: ${expected.totalCost})`)
        passedTests++
      } else {
        console.log('❌ 失败')
        console.log(`   输入费用: ${inputCost} (预期: ${expected.inputCost}) ${inputMatch ? '✓' : '✗'}`)
        console.log(`   输出费用: ${outputCost} (预期: ${expected.outputCost}) ${outputMatch ? '✓' : '✗'}`)
        console.log(`   总费用: ${totalCost} (预期: ${expected.totalCost}) ${totalMatch ? '✓' : '✗'}`)
      }
    } else {
      console.log('❌ 计算失败:', result.error)
    }
  })
  
  // 内容估算测试
  console.log('\n\n📝 内容估算测试:')
  contentEstimationTests.forEach((testCase, index) => {
    totalTests++
    console.log(`\n测试 ${index + 1}: ${testCase.name}`)
    
    const result = costCalculationService.estimateCostFromContent(testCase.input)
    
    if (result.success) {
      const { totalTokens, totalCost } = result.data
      const [minTokens, maxTokens] = testCase.expectedTokensRange
      
      if (totalTokens >= minTokens && totalTokens <= maxTokens) {
        console.log('✅ 通过')
        console.log(`   内容: "${testCase.input.content}"`)
        console.log(`   估算tokens: ${totalTokens} (预期范围: ${minTokens}-${maxTokens})`)
        console.log(`   估算费用: ¥${totalCost}`)
        passedTests++
      } else {
        console.log('❌ 失败')
        console.log(`   内容: "${testCase.input.content}"`)
        console.log(`   估算tokens: ${totalTokens} (预期范围: ${minTokens}-${maxTokens})`)
        console.log(`   估算费用: ¥${totalCost}`)
      }
    } else {
      console.log('❌ 估算失败:', result.error)
    }
  })
  
  // 批量计算测试
  console.log('\n\n📦 批量计算测试:')
  totalTests++
  const batchResult = costCalculationService.batchCalculateCost(batchCalculationTest)
  
  if (batchResult.success) {
    console.log('✅ 批量计算通过')
    console.log(`   处理记录数: ${batchResult.data.summary.totalRecords}`)
    console.log(`   总tokens: ${batchResult.data.summary.totalTokens}`)
    console.log(`   总费用: ¥${batchResult.data.summary.totalCost}`)
    
    batchResult.data.records.forEach((record, index) => {
      if (record.success) {
        console.log(`   记录${index + 1}: ${record.data.model} - ¥${record.data.totalCost}`)
      }
    })
    passedTests++
  } else {
    console.log('❌ 批量计算失败:', batchResult.error)
  }
  
  // 测试结果汇总
  console.log('\n\n📋 测试结果汇总:')
  console.log(`总测试数: ${totalTests}`)
  console.log(`通过测试: ${passedTests}`)
  console.log(`失败测试: ${totalTests - passedTests}`)
  console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！费用计算功能正常。')
  } else {
    console.log('\n⚠️ 部分测试失败，请检查费用计算逻辑。')
  }
  
  return passedTests === totalTests
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests()
}

module.exports = {
  runTests,
  testCases,
  contentEstimationTests,
  batchCalculationTest
}
