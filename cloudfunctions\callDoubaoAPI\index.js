/**
 * 通用AI模型调用云函数
 * 支持豆包AI的Chat Completions格式
 * 用于生成AI评语
 */
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  timeout: 60000 // 强制设置60秒超时
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  // 生产环境：隐藏敏感参数信息
  console.log('🎯 callDoubaoAPI云函数被调用，接收参数:', {
    style: event.style,
    length: event.length,
    hasStudentName: !!event.studentName,
    studentName: event.studentName, // 🔍 调试：显示实际学生姓名
    hasPerformanceMaterial: !!event.performanceMaterial,
    allKeys: Object.keys(event)
  });

  // 🔍 调试：详细记录学生姓名信息
  console.log('🔍 [调试] 学生姓名详细信息:', {
    原始姓名: event.studentName,
    姓名类型: typeof event.studentName,
    姓名长度: event.studentName ? event.studentName.length : 0,
    是否为空: !event.studentName || event.studentName.trim() === '',
    去空格后: event.studentName ? event.studentName.trim() : 'null'
  });
  
  try {
    const { style = 'warm', length = 'medium', temperature = 0.7, max_tokens = 300, studentName = '', performanceMaterial = '' } = event;

    // 移除旧的prompt处理逻辑，现在直接从数据库获取模板

    // 🎯 核心修改：直接从数据库获取提示词模板
    console.log('🎯 开始从数据库获取提示词模板，类型:', style);
    
    // 🔧 修复：完整的类型映射，将小程序风格精确映射到数据库类型
    const styleMapping = {
      'warm': 'gentle',          // 温暖亲切 → 温和亲切型 (数据库type: gentle)
      'formal': 'formal',        // 正式规范 → 正式规范型 (数据库type: formal)  
      'encouraging': 'encouraging', // 鼓励激励 → 鼓励激励型 (数据库type: encouraging)
      'detailed': 'detailed'     // 详细具体 → 详细具体型 (数据库type: detailed)
      // 注：数据库还有comprehensive(综合发展型)，但小程序端未使用
    };
    
    const dbType = styleMapping[style] || style;
    console.log('🎯 类型映射：', style, '→', dbType);
    
    let promptTemplate = '';
    
    try {
      const db = cloud.database();
      const templateResult = await db.collection('prompt_templates')
        .where({
          type: dbType,
          enabled: true
        })
        .orderBy('version', 'desc')
        .limit(1)
        .get();
      
      if (templateResult.data && templateResult.data.length > 0) {
        const template = templateResult.data[0];
        promptTemplate = template.content;
        console.log('🎯 成功获取数据库模板:', {
          name: template.name,
          type: template.type,
          version: template.version,
          contentLength: promptTemplate.length
        });
        
        // 🔧 修复：允许空姓名，但记录警告
        if (!studentName || studentName.trim() === '') {
          console.warn('🔍 警告：学生姓名为空，将生成通用评语');
          studentName = ''; // 确保为空字符串而不是undefined
        }

        // 🔍 调试：替换前的模板片段
        console.log('🔍 [调试] 替换前模板片段:', promptTemplate.substring(0, 200) + '...');
        console.log('🔍 [调试] 模板中包含的变量标记:', {
          包含学生姓名标记: promptTemplate.includes('{{学生姓名}}'),
          包含行为记录标记: promptTemplate.includes('{{行为记录}}'),
          学生姓名标记数量: (promptTemplate.match(/{{学生姓名}}/g) || []).length,
          行为记录标记数量: (promptTemplate.match(/{{行为记录}}/g) || []).length
        });

        // 🔧 修复：处理行为记录变量
        console.log('🔍 [调试] 行为记录数据:', {
          原始数据: performanceMaterial,
          数据类型: typeof performanceMaterial,
          数据长度: performanceMaterial ? performanceMaterial.length : 0,
          是否为空: !performanceMaterial || performanceMaterial.trim() === ''
        });

        // 确保行为记录有默认值
        const finalPerformanceMaterial = performanceMaterial && performanceMaterial.trim() !== ''
          ? performanceMaterial
          : '暂无具体表现记录，请根据学生的一般情况生成评语';

        // 替换模板变量
        promptTemplate = promptTemplate
          .replace(/{{学生姓名}}/g, studentName || '该同学')
          .replace(/{{行为记录}}/g, finalPerformanceMaterial);

        // 🔍 调试：替换后的模板片段
        console.log('🔍 [调试] 替换后模板片段:', promptTemplate.substring(0, 200) + '...');

        // 🔧 修复：全面的变量替换验证逻辑
        // 检查模板中是否还有未替换的变量标记
        const unreplacedNameVars = (promptTemplate.match(/{{学生姓名}}/g) || []).length;
        const unreplacedRecordVars = (promptTemplate.match(/{{行为记录}}/g) || []).length;
        const hasUnreplacedVariables = unreplacedNameVars > 0 || unreplacedRecordVars > 0;

        console.log('🎯 模板变量替换完成');
        console.log('🔍 [调试] 变量替换验证结果:', {
          未替换的学生姓名标记: unreplacedNameVars,
          未替换的行为记录标记: unreplacedRecordVars,
          是否有未替换变量: hasUnreplacedVariables,
          学生姓名: studentName || '(空)',
          行为记录长度: finalPerformanceMaterial.length
        });

        // 🔍 调试：统计替换后模板中关键内容
        if (studentName && studentName.trim() !== '') {
          const nameOccurrences = (promptTemplate.match(new RegExp(studentName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;
          console.log('🔍 [调试] 替换后学生姓名在模板中出现次数:', nameOccurrences);
        }

        // 🔧 修复：只有在确实存在未替换变量时才报错
        if (hasUnreplacedVariables) {
          console.error('🎯 模板变量替换失败，存在未替换的变量标记');
          console.error('🔍 详细信息:', {
            未替换学生姓名: unreplacedNameVars,
            未替换行为记录: unreplacedRecordVars,
            模板片段: promptTemplate.substring(0, 500)
          });
          return {
            success: false,
            error: '模板变量替换失败',
            message: `提示词模板中存在未替换的变量标记：学生姓名(${unreplacedNameVars})，行为记录(${unreplacedRecordVars})`
          };
        }

        // 记录替换成功信息
        console.log('✅ 所有模板变量替换成功');
        if (!studentName || studentName.trim() === '') {
          console.warn('🔍 警告：学生姓名为空，已使用通用称呼');
        }
        
        // 🔧 修复：强化姓名和内容要求
        if (studentName && studentName.trim() !== '') {
          const expectedTitle = studentName + '同学';  // 🔧 修复：使用完整姓名+同学
          promptTemplate += `\n\n【严格要求】：
1. 学生称呼必须是"${expectedTitle}"，严禁使用"${studentName.slice(-1)}同学"或其他任何称呼！
2. 评语内容必须严格基于以下行为记录："${finalPerformanceMaterial}"
3. 严禁编造任何不在行为记录中的内容（如数学成绩、作业情况、班级职务等）
4. 如果行为记录为空或无关，请明确说明"暂无具体表现记录"
5. 评语必须与提供的行为记录高度相关，不得偏离主题`;
          console.log('🔍 [调试] 期望的学生称呼:', expectedTitle);
          console.log('🔍 [调试] 行为记录内容:', finalPerformanceMaterial);
        } else {
          promptTemplate += `\n\n【关键要求】：请生成一份通用的学生评语，使用"该同学"作为称呼。`;
          console.log('🔍 [调试] 使用通用称呼模式');
        }

        console.log('🔍 [调试] 最终提示词片段:', promptTemplate.substring(0, 300) + '...');
        console.log('🔍 [调试] 最终提示词长度:', promptTemplate.length);
      } else {
        throw new Error(`未找到类型为 ${dbType} (原始类型: ${style}) 的提示词模板`);
      }
    } catch (templateError) {
      console.error('🎯 从数据库获取提示词失败:', templateError);
      return {
        success: false,
        error: `获取提示词模板失败: ${templateError.message}`,
        message: '请在管理后台检查提示词模板配置'
      };
    }

    // 从云开发环境变量获取AI配置（优先）或从数据库获取
    let aiConfig = {
      model: process.env.DOUBAO_MODEL || 'doubao-seed-1-6-flash-250715',
      apiUrl: process.env.DOUBAO_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      apiKey: process.env.DOUBAO_API_KEY,
      provider: '豆包AI'
    };

    // 如果环境变量中没有API密钥，尝试从数据库获取
    if (!aiConfig.apiKey) {
      console.log('环境变量中无API密钥，尝试从数据库获取AI配置');
      try {
        const db = cloud.database();
        
        // 先查看所有system_config数据用于调试
        console.log('🔍 调试：查看system_config集合所有数据');
        const allConfigs = await db.collection('system_config').limit(10).get();
        console.log('🔍 system_config总数量:', allConfigs.data.length);
        allConfigs.data.forEach((config, index) => {
          console.log(`🔍 配置${index + 1}:`, {
            _id: config._id,
            type: config.type,
            status: config.status,
            hasApiKey: !!config.apiKey,
            updateTime: config.updateTime,
            allFields: Object.keys(config)
          });
        });
        
        const configResult = await db.collection('system_config')
          .where({
            type: 'ai_config',
            status: 'active'
          })
          .orderBy('updateTime', 'desc')
          .limit(1)
          .get();

        console.log('🔍 AI配置查询结果数量:', configResult.data.length);
        
        if (configResult.data && configResult.data.length > 0) {
          const dbConfig = configResult.data[0];
          console.log('🔍 找到的AI配置详情:', {
            _id: dbConfig._id,
            type: dbConfig.type,
            status: dbConfig.status,
            model: dbConfig.model,
            apiUrl: dbConfig.apiUrl,
            hasApiKey: !!dbConfig.apiKey,
            apiKeyLength: dbConfig.apiKey ? dbConfig.apiKey.length : 0,
            apiKeyPrefix: dbConfig.apiKey ? dbConfig.apiKey.substring(0, 10) + '...' : 'null',
            updateTime: dbConfig.updateTime,
            allFields: Object.keys(dbConfig)
          });
          
          aiConfig = {
            model: dbConfig.model || aiConfig.model,
            apiUrl: dbConfig.apiUrl || aiConfig.apiUrl,
            apiKey: dbConfig.apiKey,
            provider: '豆包AI（数据库配置）'
          };
          console.log('从数据库获取AI配置成功:', {
            hasApiKey: !!aiConfig.apiKey,
            model: aiConfig.model,
            apiUrl: aiConfig.apiUrl
          });
        } else {
          console.log('❌ 未找到匹配的AI配置记录');
        }
      } catch (dbError) {
        console.error('从数据库获取AI配置失败:', dbError);
      }
    }

    // 验证API密钥是否存在
    if (!aiConfig.apiKey) {
      console.error('未找到有效的API密钥（环境变量和数据库都没有）');
      return {
        success: false,
        error: '系统配置错误：AI服务未正确配置',
        message: '请在管理后台配置AI服务或设置环境变量'
      };
    }

    console.log('使用AI配置:', {
      model: aiConfig.model,
      hasApiKey: !!aiConfig.apiKey,
      apiUrl: aiConfig.apiUrl
    });

    // 🎯 使用数据库模板调用豆包AI API
    console.log('🎯 准备调用AI，使用数据库提示词模板');
    
    try {
      const response = await Promise.race([
        callAIAPI({
          prompt: promptTemplate, // 🎯 直接使用从数据库获取的完整提示词
          style,
          length,
          temperature,
          max_tokens,
          config: aiConfig
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('豆包AI响应超时')), 50000) // 50秒超时
        )
      ]);
      
      // AI调用成功，进行后处理验证
      let generatedContent = response.content;

      // 🔧 修复：使用完整姓名验证
      const expectedName = studentName + '同学'; // 期望的称呼格式：完整姓名+同学
      const hasCorrectName = generatedContent.includes(expectedName);

      console.log('🔍 AI生成内容验证:');
      console.log('期望称呼:', expectedName);
      console.log('生成内容包含正确称呼:', hasCorrectName ? '✅是' : '❌否');
      console.log('生成内容预览:', generatedContent.substring(0, 100) + '...');

      // 🔧 强化内容相关性验证
      const recordKeywords = performanceMaterial ? performanceMaterial.match(/[\u4e00-\u9fa5]+/g) || [] : [];
      console.log('🔍 行为记录关键词:', recordKeywords);

      // 检查生成内容是否与行为记录相关
      let contentRelevant = false;
      if (recordKeywords.length > 0) {
        contentRelevant = recordKeywords.some(keyword =>
          keyword.length > 1 && generatedContent.includes(keyword)
        );
      }
      console.log('🔍 内容相关性检查:', contentRelevant ? '✅相关' : '❌不相关');

      // 如果AI使用了错误的姓名或内容不相关，进行修正
      let nameFixed = false;
      let contentFixed = false;

      if (!hasCorrectName) {
        console.log('🔧 检测到姓名错误，开始修正...');

        // 常见的错误称呼模式
        const wrongPatterns = [
          /[一-龥]同学/g,  // 任何单字+同学
          /小[一-龥]+同学/g, // 小XX同学
          /该同学/g,        // 该同学
          /这位同学/g       // 这位同学
        ];

        wrongPatterns.forEach(pattern => {
          if (pattern.test(generatedContent)) {
            generatedContent = generatedContent.replace(pattern, expectedName);
            console.log(`🔧 已将错误称呼替换为"${expectedName}"`);
            nameFixed = true;
          }
        });

        // 如果开头没有正确称呼，添加
        if (!generatedContent.startsWith(expectedName)) {
          generatedContent = expectedName + '，' + generatedContent;
          console.log('🔧 在评语开头添加了正确称呼');
          nameFixed = true;
        }
      }

      // 🔧 如果内容完全不相关，添加警告
      if (performanceMaterial && !contentRelevant) {
        console.warn('⚠️ 警告：AI生成内容与行为记录不符，可能需要重新生成');
        contentFixed = true;
      }
      
      return {
        success: true,
        data: {
          content: generatedContent
        },
        message: '豆包AI生成成功',
        isFallback: false,
        nameFixed: nameFixed,
        contentRelevant: contentRelevant,
        debugInfo: {
          expectedName: expectedName,
          hasCorrectName: hasCorrectName,
          recordKeywords: recordKeywords,
          contentFixed: contentFixed
        }
      };
      
    } catch (aiError) {
      console.error('豆包AI API调用失败:', aiError);
      
      return {
        success: false,
        error: `豆包AI调用失败: ${aiError.message}`,
        message: '豆包AI服务异常，请稍后重试'
      };
    }

  } catch (error) {
    console.error('云函数执行失败:', error);
    
    // 🎯 不再使用备用方案，直接返回错误
    return {
      success: false,
      error: `云函数执行失败: ${error.message}`,
      message: '评语生成失败，请重试或检查提示词模板配置'
    };
  }
};

/**
 * 调用AI API (使用内置https，避免axios加载时间)
 */
async function callAIAPI({ prompt, style, length, temperature, max_tokens, config }) {
  const https = require('https');
  const { URL } = require('url');

  // 🎯 构建请求数据 - 直接使用数据库提示词模板
  console.log('🎯 构建AI请求，prompt长度:', prompt.length);
  
  const requestData = {
    model: config.model,
    messages: [
      {
        role: 'user', 
        content: prompt // 🎯 直接使用从数据库获取的完整提示词模板
      }
    ],
    temperature: temperature,
    max_tokens: max_tokens,
    stream: false
  };

  // AI API调用中

  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(requestData);
    const parsedUrl = new URL(config.apiUrl);
    
    const options = {
      hostname: parsedUrl.hostname,
      port: 443,
      path: parsedUrl.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.choices && response.choices[0]) {
            const content = response.choices[0].message.content;
            // AI内容生成完成
            
            resolve({
              content: content.trim(),
              usage: response.usage || {
                prompt_tokens: 0,
                completion_tokens: 0,
                total_tokens: 0
              }
            });
          } else {
            console.error('AI API响应错误:', response);
            reject(new Error(`API错误: ${response.error?.message || '未知错误'}`));
          }
        } catch (parseError) {
          console.error('解析响应失败:', parseError, 'Raw data:', data);
          reject(new Error('响应解析失败'));
        }
      });
    });

    req.on('error', (error) => {
      console.error('HTTPS请求失败:', error);
      reject(error);
    });

    // 不设置超时，等待AI完全生成内容
    // req.setTimeout() 已移除，让AI有充分时间生成内容

    req.write(postData);
    req.end();
  });
}

// 🎯 移除系统提示词函数，完全依赖数据库模板
// 不再需要 getSystemPrompt 函数，因为现在直接使用数据库中的完整提示词模板

// 🎯 移除本地备用评语生成函数
// 现在完全依赖数据库模板 + AI生成，不再使用本地备用评语
// 如果AI调用失败，直接返回错误，让用户重试

