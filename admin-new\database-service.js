/**
 * 真实数据库服务
 * 连接云开发数据库，实现真正的数据读写
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用动态环境ID
})

const db = cloud.database()
const _ = db.command

class DatabaseService {
  constructor() {
    this.db = db
  }

  /**
   * 获取AI配置列表
   */
  async getAiConfigs() {
    try {
      const result = await this.db.collection('system_config')
        .where({
          type: 'ai_config'
        })
        .orderBy('updateTime', 'desc')
        .get()
      
      return result.data.map(config => ({
        id: config._id,
        name: config.name || config.model,
        model: config.model,
        provider: config.provider,
        status: config.status,
        apiKey: this.maskApiKey(config.apiKey),
        apiUrl: config.apiUrl,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        topP: config.topP,
        frequencyPenalty: config.frequencyPenalty,
        presencePenalty: config.presencePenalty,
        enableStream: config.enableStream,
        enableCache: config.enableCache,
        timeout: config.timeout,
        createTime: config.createTime,
        updateTime: config.updateTime,
        lastModified: config.updateTimestamp || Date.now()
      }))
    } catch (error) {
      console.error('获取AI配置失败:', error)
      throw new Error(`获取AI配置失败: ${error.message}`)
    }
  }

  /**
   * 获取当前活跃的AI配置
   */
  async getCurrentAiConfig() {
    try {
      const result = await this.db.collection('system_config')
        .where({
          type: 'ai_config',
          status: 'active'
        })
        .orderBy('updateTime', 'desc')
        .limit(1)
        .get()

      if (result.data && result.data.length > 0) {
        const config = result.data[0]
        return {
          id: config._id,
          type: config.type,
          status: config.status,
          model: config.model,
          provider: config.provider,
          apiKey: this.maskApiKey(config.apiKey),
          apiUrl: config.apiUrl,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
          frequencyPenalty: config.frequencyPenalty,
          presencePenalty: config.presencePenalty,
          enableStream: config.enableStream,
          enableCache: config.enableCache,
          timeout: config.timeout,
          updateTime: config.updateTime,
          lastModified: config.updateTimestamp || Date.now()
        }
      } else {
        // 返回默认配置
        return {
          type: 'ai_config',
          status: 'active',
          model: 'doubao-pro-4k',
          provider: 'bytedance',
          apiKey: 'bce-v3-****',
          apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
          temperature: 0.7,
          maxTokens: 2000,
          topP: 0.9,
          frequencyPenalty: 0,
          presencePenalty: 0,
          enableStream: false,
          enableCache: true,
          timeout: 30,
          updateTime: new Date().toISOString(),
          lastModified: Date.now() - 60000
        }
      }
    } catch (error) {
      console.error('获取当前AI配置失败:', error)
      throw new Error(`获取当前AI配置失败: ${error.message}`)
    }
  }

  /**
   * 保存AI配置
   */
  async saveAiConfig(configData) {
    try {
      // 先将现有的活跃配置设为非活跃
      await this.db.collection('system_config').where({
        type: 'ai_config',
        status: 'active'
      }).update({
        data: {
          status: 'inactive',
          updateTime: this.db.serverDate(),
          updateTimestamp: Date.now()
        }
      })

      // 创建新的AI配置
      const newConfig = {
        type: 'ai_config',
        status: 'active',
        model: configData.model,
        provider: configData.provider,
        apiKey: configData.apiKey,
        apiUrl: configData.apiUrl,
        temperature: configData.temperature || 0.7,
        maxTokens: configData.maxTokens || 2000,
        topP: configData.topP || 0.9,
        frequencyPenalty: configData.frequencyPenalty || 0,
        presencePenalty: configData.presencePenalty || 0,
        enableStream: configData.enableStream || false,
        enableCache: configData.enableCache || true,
        timeout: configData.timeout || 30,
        createTime: this.db.serverDate(),
        updateTime: this.db.serverDate(),
        createTimestamp: Date.now(),
        updateTimestamp: Date.now(),
        createdBy: 'admin',
        updatedBy: 'admin'
      }

      const result = await this.db.collection('system_config').add({
        data: newConfig
      })

      console.log('✅ AI配置保存成功:', result._id)
      
      return {
        id: result._id,
        message: 'AI配置保存成功，新配置已生效',
        config: {
          ...newConfig,
          id: result._id,
          apiKey: this.maskApiKey(newConfig.apiKey)
        }
      }
    } catch (error) {
      console.error('保存AI配置失败:', error)
      throw new Error(`保存AI配置失败: ${error.message}`)
    }
  }

  /**
   * 更新AI配置
   */
  async updateAiConfig(configId, updateData) {
    try {
      const updates = {
        updateTime: this.db.serverDate(),
        updateTimestamp: Date.now(),
        updatedBy: 'admin'
      }

      // 只更新提供的字段
      if (updateData.model) updates.model = updateData.model
      if (updateData.provider) updates.provider = updateData.provider
      if (updateData.apiKey) updates.apiKey = updateData.apiKey
      if (updateData.apiUrl) updates.apiUrl = updateData.apiUrl
      if (updateData.temperature !== undefined) updates.temperature = updateData.temperature
      if (updateData.maxTokens !== undefined) updates.maxTokens = updateData.maxTokens
      if (updateData.topP !== undefined) updates.topP = updateData.topP
      if (updateData.frequencyPenalty !== undefined) updates.frequencyPenalty = updateData.frequencyPenalty
      if (updateData.presencePenalty !== undefined) updates.presencePenalty = updateData.presencePenalty
      if (updateData.enableStream !== undefined) updates.enableStream = updateData.enableStream
      if (updateData.enableCache !== undefined) updates.enableCache = updateData.enableCache
      if (updateData.timeout !== undefined) updates.timeout = updateData.timeout
      if (updateData.status) updates.status = updateData.status

      await this.db.collection('system_config').doc(configId).update({
        data: updates
      })

      console.log('✅ AI配置更新成功:', configId)

      return {
        message: 'AI配置更新成功',
        configId,
        updates
      }
    } catch (error) {
      console.error('更新AI配置失败:', error)
      throw new Error(`更新AI配置失败: ${error.message}`)
    }
  }

  /**
   * 删除AI配置
   */
  async deleteAiConfig(configId) {
    try {
      // 检查是否是活跃配置
      const configResult = await this.db.collection('system_config').doc(configId).get()
      
      if (!configResult.data) {
        throw new Error('AI配置不存在')
      }

      if (configResult.data.status === 'active') {
        throw new Error('不能删除当前活跃的AI配置，请先切换到其他配置')
      }

      await this.db.collection('system_config').doc(configId).remove()

      console.log('✅ AI配置删除成功:', configId)

      return {
        message: 'AI配置删除成功',
        configId
      }
    } catch (error) {
      console.error('删除AI配置失败:', error)
      throw new Error(`删除AI配置失败: ${error.message}`)
    }
  }

  /**
   * 启用/停用AI配置
   */
  async toggleAiConfig(configId, enabled) {
    try {
      if (enabled) {
        // 先将所有配置设为非活跃
        await this.db.collection('system_config').where({
          type: 'ai_config',
          status: 'active'
        }).update({
          data: {
            status: 'inactive',
            updateTime: this.db.serverDate(),
            updateTimestamp: Date.now()
          }
        })

        // 启用指定配置
        await this.db.collection('system_config').doc(configId).update({
          data: {
            status: 'active',
            updateTime: this.db.serverDate(),
            updateTimestamp: Date.now()
          }
        })

        console.log('✅ AI配置启用成功:', configId)
        return { message: 'AI配置启用成功' }
      } else {
        // 停用配置
        await this.db.collection('system_config').doc(configId).update({
          data: {
            status: 'inactive',
            updateTime: this.db.serverDate(),
            updateTimestamp: Date.now()
          }
        })

        console.log('✅ AI配置停用成功:', configId)
        return { message: 'AI配置停用成功' }
      }
    } catch (error) {
      console.error('切换AI配置状态失败:', error)
      throw new Error(`切换AI配置状态失败: ${error.message}`)
    }
  }

  /**
   * 检查配置变化
   */
  async checkConfigChanges(clientLastModified) {
    try {
      const currentConfig = await this.getCurrentAiConfig()
      const serverLastModified = currentConfig.lastModified || 0
      const clientTime = clientLastModified || 0

      return {
        hasChanges: serverLastModified > clientTime,
        serverLastModified,
        clientLastModified: clientTime,
        currentConfig: serverLastModified > clientTime ? currentConfig : null
      }
    } catch (error) {
      console.error('检查配置变化失败:', error)
      throw new Error(`检查配置变化失败: ${error.message}`)
    }
  }

  /**
   * 掩码API密钥
   */
  maskApiKey(apiKey) {
    if (!apiKey) return ''
    const keyLength = apiKey.length
    if (keyLength <= 8) return apiKey
    
    return apiKey.substring(0, 4) + '*'.repeat(Math.max(0, keyLength - 8)) + apiKey.substring(Math.max(4, keyLength - 4))
  }

  /**
   * 测试数据库连接
   */
  async testConnection() {
    try {
      const result = await this.db.collection('system_config').limit(1).get()
      return {
        success: true,
        message: '数据库连接正常',
        collections: ['system_config'],
        sampleData: result.data[0] || null
      }
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  }

  // 其他数据服务方法保持不变...
  async getDashboardStats() {
    // 实现仪表板统计数据获取
    return {
      totalUsers: 0,
      todayComments: 0,
      aiCalls: 0,
      satisfaction: 0,
      lastUpdated: new Date().toISOString()
    }
  }

  async getRecentActivities(limit = 10) {
    // 实现最近活动数据获取
    return []
  }

  async getStudents(params = {}) {
    // 实现学生数据获取
    return {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 10
    }
  }

  async getComments(params = {}) {
    // 实现评语数据获取
    return {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 10
    }
  }
}

module.exports = new DatabaseService()