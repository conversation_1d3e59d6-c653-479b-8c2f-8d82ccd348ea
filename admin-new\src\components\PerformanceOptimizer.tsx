import React, { useEffect, useState, useMemo } from 'react'
import { Card, Button, Typography, Space, Progress, Alert, Tag } from 'antd'
import { <PERSON>boltOutlined, WarningOutlined, CheckCircleOutlined } from '@ant-design/icons'
import PerformanceDiagnostic, { 
  checkNetworkPerformance, 
  detectDevicePerformance, 
  getOptimizationSuggestions 
} from '../utils/performanceDiagnostic'

const { Title, Text } = Typography

// 性能优化器组件
const PerformanceOptimizer: React.FC = () => {
  const [diagnostic] = useState(() => new PerformanceDiagnostic())
  const [performanceReport, setPerformanceReport] = useState<any>(null)
  const [networkSpeed, setNetworkSpeed] = useState<string>('unknown')
  const [deviceLevel, setDeviceLevel] = useState<string>('unknown')
  const [isOptimizing, setIsOptimizing] = useState(false)

  useEffect(() => {
    // 初始化性能检测
    const initDiagnostic = async () => {
      const network = await checkNetworkPerformance()
      const device = detectDevicePerformance()
      
      setNetworkSpeed(network)
      setDeviceLevel(device)
      
      // 定期更新性能报告
      const interval = setInterval(() => {
        const report = diagnostic.getPerformanceReport()
        setPerformanceReport(report)
      }, 2000)
      
      return () => {
        clearInterval(interval)
        diagnostic.destroy()
      }
    }
    
    initDiagnostic()
  }, [diagnostic])

  // 获取性能等级颜色
  const getPerformanceColor = (value: number, type: 'fps' | 'memory' | 'latency') => {
    switch (type) {
      case 'fps':
        if (value >= 50) return 'success'
        if (value >= 30) return 'warning'
        return 'error'
      case 'memory':
        if (value <= 50) return 'success'
        if (value <= 100) return 'warning'
        return 'error'
      case 'latency':
        if (value <= 100) return 'success'
        if (value <= 300) return 'warning'
        return 'error'
      default:
        return 'default'
    }
  }

  // 自动优化建议
  const optimizationSuggestions = useMemo(() => {
    return getOptimizationSuggestions()
  }, [deviceLevel])

  // 一键优化
  const handleOptimize = async () => {
    setIsOptimizing(true)
    
    try {
      // 清理内存
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc()
      }
      
      // 清理无用的事件监听器
      const oldListeners = document.querySelectorAll('[data-optimized]')
      oldListeners.forEach(el => el.remove())
      
      // 优化图片加载
      const images = document.querySelectorAll('img[src]')
      images.forEach((img: Element) => {
        const imgEl = img as HTMLImageElement
        if (!imgEl.loading) {
          imgEl.loading = 'lazy'
        }
      })
      
      // 延迟加载非关键CSS
      const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
      stylesheets.forEach((link: Element, index) => {
        if (index > 2) { // 保留前3个关键样式表
          const linkEl = link as HTMLLinkElement
          linkEl.media = 'print'
          linkEl.onload = () => { linkEl.media = 'all' }
        }
      })
      
      // 预连接重要域名
      const preconnectDomains = [
        'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com',
        'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la'
      ]
      
      preconnectDomains.forEach(domain => {
        const link = document.createElement('link')
        link.rel = 'preconnect'
        link.href = domain
        document.head.appendChild(link)
      })
      
      await new Promise(resolve => setTimeout(resolve, 2000))
      
    } finally {
      setIsOptimizing(false)
    }
  }

  const currentFPS = performanceReport?.fps?.latest || 0
  const currentMemory = performanceReport?.memory?.latest || 0

  return (
    <div className="performance-optimizer p-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <Title level={4} className="!mb-0">
            <ThunderboltOutlined className="mr-2" />
            性能监控与优化
          </Title>
          <Button 
            type="primary"
            loading={isOptimizing}
            onClick={handleOptimize}
            icon={<ThunderboltOutlined />}
          >
            一键优化
          </Button>
        </div>

        {/* 实时性能指标 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card size="small" className="text-center">
            <div className="text-2xl font-bold mb-2">
              <span className={`text-${getPerformanceColor(currentFPS, 'fps')}`}>
                {currentFPS}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-300 ml-1">FPS</span>
            </div>
            <Progress
              percent={Math.min(100, (currentFPS / 60) * 100)}
              status={getPerformanceColor(currentFPS, 'fps') as any}
              showInfo={false}
              size="small"
            />
            <Text type="secondary">帧率</Text>
          </Card>

          <Card size="small" className="text-center">
            <div className="text-2xl font-bold mb-2">
              <span className={`text-${getPerformanceColor(currentMemory, 'memory')}`}>
                {currentMemory}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-300 ml-1">MB</span>
            </div>
            <Progress
              percent={Math.min(100, (currentMemory / 200) * 100)}
              status={getPerformanceColor(currentMemory, 'memory') as any}
              showInfo={false}
              size="small"
            />
            <Text type="secondary">内存使用</Text>
          </Card>

          <Card size="small" className="text-center">
            <div className="text-2xl font-bold mb-2">
              <Tag color={networkSpeed === 'fast' ? 'green' : networkSpeed === 'medium' ? 'orange' : 'red'}>
                {networkSpeed === 'fast' ? '快速' : networkSpeed === 'medium' ? '一般' : '缓慢'}
              </Tag>
            </div>
            <div className="mt-2">
              <Tag color={deviceLevel === 'high' ? 'blue' : deviceLevel === 'medium' ? 'orange' : 'red'}>
                {deviceLevel === 'high' ? '高性能设备' : deviceLevel === 'medium' ? '中等设备' : '低性能设备'}
              </Tag>
            </div>
            <Text type="secondary">设备性能</Text>
          </Card>
        </div>

        {/* 性能警告 */}
        {(currentFPS < 30 || currentMemory > 100) && (
          <Alert
            message="性能警告"
            description={
              <Space direction="vertical" size="small">
                {currentFPS < 30 && <Text>• 帧率过低，页面可能出现卡顿</Text>}
                {currentMemory > 100 && <Text>• 内存使用过高，建议关闭其他标签页</Text>}
              </Space>
            }
            type="warning"
            icon={<WarningOutlined />}
            showIcon
            className="mb-4"
          />
        )}

        {/* 优化建议 */}
        <Card size="small" title="个性化优化建议">
          <Space direction="vertical" size="small" className="w-full">
            {optimizationSuggestions.suggestions.map((suggestion, index) => (
              <div key={index} className="flex items-center">
                <CheckCircleOutlined className="text-green-500 mr-2" />
                <Text>{suggestion}</Text>
              </div>
            ))}
          </Space>
        </Card>

        {/* 快速诊断 */}
        <div className="mt-4">
          <Button
            type="link"
            onClick={() => {
              const diagnosis = diagnostic.quickDiagnose()
              console.group('🔍 快速性能诊断')
              console.log('问题:', diagnosis.issues)
              console.log('建议:', diagnosis.suggestions)
              console.groupEnd()
            }}
          >
            在控制台查看详细诊断
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default PerformanceOptimizer