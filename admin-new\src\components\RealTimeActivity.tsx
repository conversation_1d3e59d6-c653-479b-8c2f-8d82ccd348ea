import React, { useState, useEffect, useMemo } from 'react'
import { Card, Table, Typography, Tag, Avatar, notification, Button } from 'antd'
import { 
  UserOutlined, 
  MessageOutlined, 
  RobotOutlined, 
  EyeOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { useDataReceiver } from '../hooks/useDataReceiver'
import { dataService } from '../services/index'

const { Title } = Typography

interface RealTimeActivityProps {
  className?: string
}

interface ActivityItem {
  id: string
  user: string
  action: string
  time: string
  type: 'user' | 'comment' | 'system' | 'ai'
  status: 'success' | 'processing' | 'warning'
  details?: string
}

const RealTimeActivity: React.FC<RealTimeActivityProps> = ({ className }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [realActivities, setRealActivities] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // 使用实时数据接收器（保留原有功能）
  const { activities, isConnected } = useDataReceiver({
    enabled: true,
    autoConnect: true
  })

  // 获取真实活动数据
  const fetchRealActivities = async () => {
    try {
      setLoading(true)
      const data = await dataService.getRecentActivities(10)
      console.log('✅ 获取实时动态数据:', data)
      setRealActivities(data)
    } catch (error) {
      console.error('❌ 获取实时动态失败:', error)
      notification.error({
        message: '实时动态获取失败',
        description: `无法获取活动记录: ${error instanceof Error ? error.message : '网络连接错误'}`,
        placement: 'topRight',
        duration: 5,
        btn: (
          <Button type="primary" size="small" onClick={fetchRealActivities}>
            重新获取
          </Button>
        )
      })
      setRealActivities([])
    } finally {
      setLoading(false)
    }
  }

  // 组件加载时获取数据
  useEffect(() => {
    fetchRealActivities()

    // 停止自动刷新，使用静态数据
    // const interval = setInterval(fetchRealActivities, 15000)
    console.log('🔄 RealTimeActivity使用静态数据，已停止自动刷新')

    // return () => clearInterval(interval)
  }, [])

  // 处理实时数据 - 只使用真实数据
  const activityData = useMemo(() => {
    if (realActivities.length > 0) {
      return realActivities.slice(0, 8).map((activity, index) => ({
        id: activity.id || `real_${index}`,
        user: activity.userName || '未知用户',
        action: activity.action || '未知操作',
        time: new Date(activity.timestamp).toLocaleTimeString(),
        type: (activity.actionType === 'comment_generate' ? 'comment' :
               activity.actionType === 'user_login' ? 'user' :
               activity.actionType === 'ai_call' ? 'ai' : 'system') as const,
        status: 'success' as const,
        details: activity.metadata ? JSON.stringify(activity.metadata) : ''
      }))
    }

    // 如果没有真实数据，返回空数组
    return []
  }, [realActivities])

  // 自动滚动效果
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % Math.max(1, activityData.length - 4))
    }, 3000) // 每3秒滚动一次

    return () => clearInterval(timer)
  }, [activityData.length])

  // 获取图标
  const getIcon = (type: string) => {
    switch (type) {
      case 'user': return <UserOutlined />
      case 'comment': return <MessageOutlined />
      case 'ai': return <RobotOutlined />
      case 'system': return <ThunderboltOutlined />
      default: return <EyeOutlined />
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined className="text-green-500" />
      case 'processing': return <ClockCircleOutlined className="text-blue-500" />
      case 'warning': return <ClockCircleOutlined className="text-orange-500" />
      default: return <CheckCircleOutlined className="text-gray-500 dark:text-gray-400" />
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success'
      case 'processing': return 'processing'
      case 'warning': return 'warning'
      default: return 'default'
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      width: 120,
      render: (user: string, record: ActivityItem) => (
        <div className="flex items-center gap-2">
          <Avatar 
            size={24} 
            icon={getIcon(record.type)}
            className="bg-gradient-to-r from-blue-500 to-purple-600"
          />
          <span className="font-medium text-gray-800 dark:text-gray-100">{user}</span>
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => (
        <span className="text-gray-700 dark:text-gray-200">{action}</span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status === 'success' ? '成功' : status === 'processing' ? '进行中' : '警告'}
        </Tag>
      )
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 100,
      render: (time: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-300">{time}</span>
      )
    }
  ]

  // 显示的数据（滚动窗口）
  const visibleData = activityData.slice(currentIndex, currentIndex + 5)

  return (
    <Card 
      className={className}
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <EyeOutlined className="text-white text-sm" />
            </div>
            <span>实时动态</span>
            {realActivities.length > 0 && !loading ? (
              <div className="flex items-center gap-1 text-green-500 text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                实时数据
              </div>
            ) : (
              <div className="flex items-center gap-1 text-gray-400 dark:text-gray-500 text-xs">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                {loading ? '连接中...' : '无数据'}
              </div>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-300">
            自动滚动 • {activityData.length} 条记录
          </div>
        </div>
      }
    >
      <div className="relative overflow-hidden">
        <Table
          columns={columns}
          dataSource={visibleData}
          pagination={false}
          size="small"
          showHeader={false}
          className="realtime-activity-table"
          scroll={{ x: 600 }}
          rowKey="id"
        />
        
        {/* 滚动指示器 */}
        <div className="flex justify-center mt-3 gap-1">
          {Array.from({ length: Math.max(1, activityData.length - 4) }).map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-blue-500 w-4' 
                  : 'bg-gray-300 dark:bg-gray-600'
              }`}
            />
          ))}
        </div>
      </div>

      {/* 自定义样式 */}
      <style>{`
        .realtime-activity-table .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f0f0f0;
          padding: 8px 12px;
        }
        .realtime-activity-table .ant-table-tbody > tr:hover > td {
          background: #f8fafc;
        }
        [data-theme="dark"] .realtime-activity-table .ant-table-tbody > tr > td {
          border-bottom-color: #374151;
        }
        [data-theme="dark"] .realtime-activity-table .ant-table-tbody > tr:hover > td {
          background: #374151;
        }
      `}</style>
    </Card>
  )
}

export default RealTimeActivity
