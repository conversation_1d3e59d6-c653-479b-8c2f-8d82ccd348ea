/**
 * 系统配置表初始化脚本
 * 用于存储AI配置、系统设置等
 */

// 创建系统配置集合
db.createCollection('system_config');

// 创建索引
db.system_config.createIndex({ "type": 1, "status": 1 });
db.system_config.createIndex({ "updateTime": -1 });

// 插入默认AI配置
db.system_config.insertOne({
  type: 'ai_config',
  status: 'active',
  enabled: false,
  apiKey: '',
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  model: 'doubao-pro-4k',
  provider: 'bytedance',
  temperature: 0.7,
  maxTokens: 500,
  topP: 0.9,
  frequencyPenalty: 0,
  presencePenalty: 0,
  enableStream: false,
  enableCache: true,
  timeout: 30,
  inputPrice: 0.00075,  // 输入定价：0.00075元/千tokens
  outputPrice: 0.0030,  // 输出定价：0.0030元/千tokens
  description: '豆包AI配置 - 用于智能评语生成',
  createTime: new Date(),
  updateTime: new Date(),
  createTimestamp: Date.now(),
  updateTimestamp: Date.now(),
  createBy: 'system',
  updateBy: 'system'
});

// 插入系统基础配置
db.system_config.insertOne({
  type: 'system_basic',
  status: 'active',
  appName: '评语灵感君',
  appVersion: '3.0.0',
  appDescription: 'AI智能评语生成助手',
  maxStudentsPerClass: 50,
  maxCommentsPerBatch: 20,
  commentMinLength: 50,
  commentMaxLength: 300,
  createTime: new Date(),
  updateTime: new Date(),
  createBy: 'system',
  updateBy: 'system'
});

// 插入评语模板配置
db.system_config.insertOne({
  type: 'comment_templates',
  status: 'active',
  templates: {
    formal: {
      name: '正式严谨',
      description: '适合正式场合，用词规范',
      prompt: '请使用正式、规范的语言风格，体现教师的专业性'
    },
    warm: {
      name: '温和亲切',
      description: '语气温和，拉近师生距离',
      prompt: '请使用温和、亲切的语言风格，体现教师的关爱'
    },
    encouraging: {
      name: '激励向上',
      description: '积极正面，激发学生潜能',
      prompt: '请使用积极、激励的语言风格，激发学生的学习动力'
    },
    detailed: {
      name: '详细具体',
      description: '内容详实，分析深入',
      prompt: '请提供详细、具体的分析和建议，内容要充实'
    }
  },
  createTime: new Date(),
  updateTime: new Date(),
  createBy: 'system',
  updateBy: 'system'
});

console.log('系统配置表初始化完成');

// 数据库权限设置
// 管理员可以读写所有配置
// 普通用户只能读取部分配置（不包含敏感信息）
