/**
 * 升级system_config集合，为AI配置添加输入定价和输出定价字段
 * 运行方式: node upgrade-system-config-pricing.js
 */

const tcb = require('@cloudbase/node-sdk')

// 云开发配置
const app = tcb.init({
  env: 'cloud1-4g85f8xlb8166ff1', // 替换为你的云开发环境ID
  region: 'ap-shanghai'
})

const db = app.database()

async function upgradeSystemConfigPricing() {
  try {
    console.log('🔧 开始升级system_config集合，添加定价字段...')
    
    // 1. 查询所有ai_config类型的记录
    const { data: configs } = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    console.log(`📊 找到 ${configs.length} 条AI配置记录`)
    
    if (configs.length === 0) {
      console.log('📝 没有找到现有配置，创建默认AI配置...')
      
      // 创建默认配置（包含定价）
      const defaultConfig = {
        type: 'ai_config',
        status: 'active',
        model: 'doubao-pro-32k',
        provider: 'bytedance',
        apiKey: 'bce-v3-***', // 请替换为实际的API Key
        apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        temperature: 0.7,
        maxTokens: 2000,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        enableStream: false,
        enableCache: true,
        timeout: 30,
        // 🔥 新增定价字段
        inputPrice: 0.001,   // 输入定价：0.001元/千tokens
        outputPrice: 0.002,  // 输出定价：0.002元/千tokens
        createTime: Date.now(),
        updateTime: Date.now()
      }
      
      const result = await db.collection('system_config').add(defaultConfig)
      console.log('✅ 默认AI配置创建成功:', result.id)
    } else {
      // 2. 更新现有记录，添加定价字段
      let updatedCount = 0
      
      for (const config of configs) {
        // 检查是否已经有定价字段
        if (config.inputPrice !== undefined && config.outputPrice !== undefined) {
          console.log(`⏭️ 配置 ${config._id} 已包含定价字段，跳过`)
          continue
        }
        
        // 根据provider设置默认定价
        let defaultInputPrice = 0.001   // 默认输入定价
        let defaultOutputPrice = 0.002  // 默认输出定价
        
        switch (config.provider) {
          case 'bytedance':
            defaultInputPrice = 0.001   // 豆包模型输入定价
            defaultOutputPrice = 0.002  // 豆包模型输出定价
            break
          case 'openai':
            defaultInputPrice = 0.00750 // GPT-4输入定价
            defaultOutputPrice = 0.0300 // GPT-4输出定价
            break
          case 'anthropic':
            defaultInputPrice = 0.00300 // Claude输入定价
            defaultOutputPrice = 0.0150 // Claude输出定价
            break
          default:
            defaultInputPrice = 0.001
            defaultOutputPrice = 0.002
        }
        
        const updateData = {
          inputPrice: config.inputPrice || defaultInputPrice,
          outputPrice: config.outputPrice || defaultOutputPrice,
          updateTime: Date.now()
        }
        
        await db.collection('system_config').doc(config._id).update(updateData)
        updatedCount++
        
        console.log(`✅ 更新配置 ${config._id}:`, {
          provider: config.provider,
          inputPrice: updateData.inputPrice,
          outputPrice: updateData.outputPrice
        })
      }
      
      console.log(`🎉 成功更新 ${updatedCount} 条AI配置记录`)
    }
    
    // 3. 验证更新结果
    console.log('\n🔍 验证更新结果...')
    const { data: updatedConfigs } = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    updatedConfigs.forEach(config => {
      console.log(`📋 配置ID: ${config._id}`)
      console.log(`   模型: ${config.provider}/${config.model}`)
      console.log(`   输入定价: ¥${config.inputPrice || 0}/千tokens`)
      console.log(`   输出定价: ¥${config.outputPrice || 0}/千tokens`)
      console.log('---')
    })
    
    console.log('✅ system_config集合定价字段升级完成！')
    
  } catch (error) {
    console.error('❌ 升级失败:', error)
    throw error
  }
}

// 运行升级脚本
if (require.main === module) {
  upgradeSystemConfigPricing()
    .then(() => {
      console.log('🎉 升级完成，可以关闭此程序')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 升级出错:', error)
      process.exit(1)
    })
}

module.exports = { upgradeSystemConfigPricing }