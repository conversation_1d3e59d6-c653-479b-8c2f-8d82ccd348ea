/**
 * 简单检查和升级现有数据
 */

const tcb = require('@cloudbase/node-sdk')

const app = tcb.init({
  env: 'cloud1-4g85f8xlb8166ff1',
  region: 'ap-shanghai'
})

const db = app.database()

async function simpleUpgrade() {
  try {
    console.log('🔍 检查system_config集合...')
    
    // 查询所有可能的AI配置
    const { data: allData } = await db.collection('system_config').get()
    console.log(`找到 ${allData.length} 条记录`)
    
    // 找出需要升级的记录
    const needsUpgrade = allData.filter(item => 
      (item.model || item.provider || item.apiKey) && // 像AI配置
      (item.inputPrice === undefined || item.outputPrice === undefined) // 缺少定价
    )
    
    console.log(`需要升级 ${needsUpgrade.length} 条记录`)
    
    // 升级每条记录
    for (const item of needsUpgrade) {
      const updateData = {
        type: item.type || 'ai_config',
        inputPrice: item.inputPrice || (item.provider === 'openai' ? 0.0075 : 0.001),
        outputPrice: item.outputPrice || (item.provider === 'openai' ? 0.030 : 0.002),
        updateTime: Date.now()
      }
      
      if (!item.createTime) {
        updateData.createTime = Date.now()
      }
      
      await db.collection('system_config').doc(item._id).update(updateData)
      console.log(`✅ 升级记录: ${item._id}`)
    }
    
    // 验证结果
    const { data: aiConfigs } = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    console.log(`\n🎉 升级完成！现在有 ${aiConfigs.length} 条AI配置：`)
    aiConfigs.forEach(config => {
      console.log(`- ${config.provider}/${config.model}: 输入¥${config.inputPrice}, 输出¥${config.outputPrice}`)
    })
    
    console.log('\n✅ 现在可以在管理后台测试了！')
    
  } catch (error) {
    console.error('❌ 升级失败:', error)
  }
}

simpleUpgrade()