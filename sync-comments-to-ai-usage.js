/**
 * 数据迁移脚本：将comments数据同步到ai_usage费用统计
 * 解决费用统计显示为0的问题
 */

const cloudbase = require('@cloudbase/node-sdk')

// 初始化云开发环境
const app = cloudbase.init({
  env: 'cloud1-4g85f8xlb8166ff1',
  region: 'ap-shanghai'
})

const db = app.database()
const _ = db.command

// 费用计算服务
class CostCalculator {
  constructor() {
    this.pricing = {
      'doubao-pro-4k': {
        inputPrice: 0.00075,
        outputPrice: 0.0030,
        inputPricePerToken: 0.00075 / 1000,
        outputPricePerToken: 0.0030 / 1000
      }
    }
  }

  calculateCost(tokensUsed = 0, model = 'doubao-pro-4k') {
    const pricing = this.pricing[model] || this.pricing['doubao-pro-4k']
    
    // 简单估算：假设输入输出比例为3:1
    const inputTokens = Math.ceil(tokensUsed * 0.75)
    const outputTokens = Math.ceil(tokensUsed * 0.25)
    
    const inputCost = inputTokens * pricing.inputPricePerToken
    const outputCost = outputTokens * pricing.outputPricePerToken
    const totalCost = inputCost + outputCost

    return {
      inputTokens,
      outputTokens,
      inputCost: Math.round(inputCost * 100000) / 100000,
      outputCost: Math.round(outputCost * 100000) / 100000,
      totalCost: Math.round(totalCost * 100000) / 100000
    }
  }
}

async function migrateCommentsToAIUsage() {
  console.log('🔄 开始费用数据迁移...\n')

  const calculator = new CostCalculator()
  let processed = 0
  let skipped = 0
  let errors = 0

  try {
    // 获取当前AI配置
    const aiConfig = await db.collection('system_config')
      .where({ type: 'ai_config', status: 'active' })
      .limit(1)
      .get()
    
    const activeModel = aiConfig.data[0] || { model: 'doubao-pro-4k' }
    console.log(`✅ 使用AI模型: ${activeModel.model}`)

    // 获取所有comments
    const commentsResult = await db.collection('comments')
      .get()

    console.log(`📊 发现 ${commentsResult.data.length} 条评语记录\n`)

    // 分批处理，避免超时
    const batchSize = 50
    const total = commentsResult.data.length

    for (let i = 0; i < total; i += batchSize) {
      const batch = commentsResult.data.slice(i, i + batchSize)
      
      console.log(`📝 处理第 ${i + 1} - ${Math.min(i + batchSize, total)} 条记录...`)

      const operations = []

      for (const comment of batch) {
        try {
          // 检查是否已存在ai_usage记录
          const existing = await db.collection('ai_usage')
            .where({ sourceRecordId: comment._id })
            .count()

          if (existing.total > 0) {
            skipped++
            continue
          }

          // 计算tokens和费用
          const normalizedTokens = comment.tokensUsed || 
            Math.ceil((comment.content?.length || 50) * 1.5)
          
          const costResult = calculator.calculateCost(
            normalizedTokens, 
            activeModel.model
          )

          const aiUsage = {
            teacherId: comment.teacherId,
            sourceRecordId: comment._id,
            aiModel: activeModel.model,
            tokensUsed: normalizedTokens,
            inputTokens: costResult.inputTokens,
            outputTokens: costResult.outputTokens,
            inputCost: costResult.inputCost,
            outputCost: costResult.outputCost,
            totalCost: costResult.totalCost,
            modelInputPrice: activeModel.inputPrice || 0.00075,
            modelOutputPrice: activeModel.outputPrice || 0.0030,
            actionType: 'comment_generate',
            status: 'success',
            createTime: comment.createTime,
            updateTime: new Date()
          }

          operations.push(aiUsage)
          processed++

        } catch (error) {
          console.error(`❌ 处理记录 ${comment._id} 失败:`, error.message)
          errors++
        }
      }

      // 批量写入
      if (operations.length > 0) {
        await Promise.all(operations.map(record => 
          db.collection('ai_usage').add({ data: record })
        ))
        console.log(`✅ 已插入 ${operations.length} 条费用记录`)
      }

      // 等待避免API限制
      if (i + batchSize < total) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // 验证迁移结果
    const finalCount = await db.collection('ai_usage').count()
    console.log('\n🎉 迁移完成！')
    console.log(`📊 处理结果：
    - 新增费用记录: ${processed}
    - 跳过重复: ${skipped}
    - 错误: ${errors}
    - 总ai_usage记录: ${finalCount.total}`)

    // 验证费用统计
    const totalCost = await db.collection('ai_usage')
      .aggregate()
      .group({
        _id: null,
        totalSpent: _.sum('$totalCost'),
        totalTokens: _.sum('$tokensUsed'),
        totalCalls: _.sum(1)
      })
      .end()

    if (totalCost.list.length > 0) {
      const stats = totalCost.list[0]
      console.log(`\n💰 费用统计结果：
      - 总消耗费用: ￥${stats.totalSpent.toFixed(6)}
      - 总tokens: ${stats.totalTokens}
      - 总调用次数: ${stats.totalCalls}
      - 平均费用/调用: ￥${(stats.totalSpent / stats.totalCalls).toFixed(6)}`)
    }

    console.log('\n✅ 现在费用统计应该能正常显示了！')

  } catch (error) {
    console.error('❌ 迁移失败:', error)
    throw error
  }
}

// 运行迁移
migrateCommentsToAIUsage()
  .then(() => {
    console.log('\n🔧 需要手动执行？运行：')
    console.log('node sync-comments-to-ai-usage.js')
  })
  .catch(() => {
    console.log('\n💡 如果连接失败，可以使用：')
    console.log('npm install @cloudbase/node-sdk')
    console.log('node sync-comments-to-ai-usage.js')
  })