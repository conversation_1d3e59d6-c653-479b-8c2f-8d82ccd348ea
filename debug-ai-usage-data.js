/**
 * 调试AI使用数据和费用计算
 * 检查ai_usage集合中的数据结构和费用字段
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

async function debugAIUsageData() {
  try {
    console.log('🔍 开始调试AI使用数据...')
    
    // 1. 检查ai_usage集合是否存在数据
    console.log('\n📊 1. 检查ai_usage集合数据量')
    const usageCount = await db.collection('ai_usage').count()
    console.log(`ai_usage集合总记录数: ${usageCount.total}`)
    
    if (usageCount.total === 0) {
      console.log('❌ ai_usage集合为空，这是费用统计不显示的主要原因')
      
      // 检查comments集合
      const commentsCount = await db.collection('comments').count()
      console.log(`comments集合总记录数: ${commentsCount.total}`)
      
      if (commentsCount.total > 0) {
        console.log('💡 建议：运行sync-comments-to-ai-usage.js脚本同步数据')
      }
      return
    }
    
    // 2. 检查ai_usage集合的数据结构
    console.log('\n📊 2. 检查ai_usage集合数据结构')
    const sampleData = await db.collection('ai_usage').limit(5).get()
    
    if (sampleData.data.length > 0) {
      console.log('ai_usage样本数据:')
      sampleData.data.forEach((item, index) => {
        console.log(`记录${index + 1}:`, {
          _id: item._id,
          teacherId: item.teacherId,
          tokensUsed: item.tokensUsed,
          inputTokens: item.inputTokens,
          outputTokens: item.outputTokens,
          cost: item.cost,
          inputCost: item.inputCost,
          outputCost: item.outputCost,
          modelInputPrice: item.modelInputPrice,
          modelOutputPrice: item.modelOutputPrice,
          createTime: item.createTime
        })
      })
    }
    
    // 3. 统计费用字段的完整性
    console.log('\n📊 3. 统计费用字段完整性')
    const costFieldStats = await db.collection('ai_usage')
      .aggregate()
      .group({
        _id: null,
        totalRecords: { $sum: 1 },
        recordsWithCost: {
          $sum: {
            $cond: [{ $gt: ['$cost', 0] }, 1, 0]
          }
        },
        recordsWithInputCost: {
          $sum: {
            $cond: [{ $gt: ['$inputCost', 0] }, 1, 0]
          }
        },
        recordsWithOutputCost: {
          $sum: {
            $cond: [{ $gt: ['$outputCost', 0] }, 1, 0]
          }
        },
        totalCostSum: { $sum: '$cost' },
        totalInputCostSum: { $sum: '$inputCost' },
        totalOutputCostSum: { $sum: '$outputCost' }
      })
      .end()
    
    if (costFieldStats.list.length > 0) {
      const stats = costFieldStats.list[0]
      console.log('费用字段统计:', {
        总记录数: stats.totalRecords,
        有费用的记录数: stats.recordsWithCost,
        有输入费用的记录数: stats.recordsWithInputCost,
        有输出费用的记录数: stats.recordsWithOutputCost,
        总费用: stats.totalCostSum,
        总输入费用: stats.totalInputCostSum,
        总输出费用: stats.totalOutputCostSum
      })
      
      if (stats.recordsWithCost === 0) {
        console.log('❌ 所有记录的费用字段都为0或null，需要重新计算费用')
      }
    }
    
    // 4. 检查system_config集合的定价配置
    console.log('\n📊 4. 检查system_config集合定价配置')
    const configs = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    if (configs.data.length > 0) {
      console.log('AI配置定价信息:')
      configs.data.forEach((config, index) => {
        console.log(`配置${index + 1}:`, {
          _id: config._id,
          model: config.model,
          provider: config.provider,
          inputPrice: config.inputPrice,
          outputPrice: config.outputPrice,
          status: config.status
        })
      })
    } else {
      console.log('❌ 未找到AI配置定价信息')
    }
    
    // 5. 测试getCostStats云函数
    console.log('\n📊 5. 测试getCostStats云函数')
    try {
      const costStatsResult = await cloud.callFunction({
        name: 'getCostStats',
        data: { action: 'getAllCostStats' }
      })
      
      console.log('getCostStats返回结果:', {
        success: costStatsResult.result?.success,
        totalCost: costStatsResult.result?.data?.total?.totalCost,
        dailyCost: costStatsResult.result?.data?.daily?.todayCost,
        teachersCount: costStatsResult.result?.data?.teachers?.length || 0,
        error: costStatsResult.result?.error
      })
    } catch (error) {
      console.error('❌ getCostStats云函数调用失败:', error)
    }
    
    console.log('\n✅ 调试完成')
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
  }
}

// 运行调试
debugAIUsageData()
