/**
 * 更新数据库中的提示词模板
 * 将修复后的模板同步到prompt_templates集合
 */

const fs = require('fs');
const path = require('path');

// 读取更新后的模板数据
function loadUpdatedTemplates() {
  try {
    const templatesPath = path.join(__dirname, 'prompt-templates-data.json');
    const templatesData = fs.readFileSync(templatesPath, 'utf8');
    const templates = JSON.parse(templatesData);
    
    console.log('✅ 成功加载更新后的模板数据');
    console.log(`📊 模板数量: ${templates.length}`);
    
    // 验证模板内容是否包含姓名强化指令
    templates.forEach(template => {
      const hasNameRequirement = template.content.includes('**重要提醒：必须使用提供的学生真实姓名');
      const hasNameConstraint = template.content.includes('**姓名要求**');
      
      console.log(`📋 ${template.name} (${template.type}):`);
      console.log(`   - 包含姓名提醒: ${hasNameRequirement ? '✅是' : '❌否'}`);
      console.log(`   - 包含姓名约束: ${hasNameConstraint ? '✅是' : '❌否'}`);
    });
    
    return templates;
  } catch (error) {
    console.error('❌ 加载模板数据失败:', error);
    return null;
  }
}

// 生成部署指令
function generateDeployInstructions(templates) {
  console.log('\n🚀 部署指令:');
  console.log('1. 确保云函数 managePromptTemplates 已部署');
  console.log('2. 运行以下命令更新数据库模板:');
  console.log('   node init-prompt-templates.js');
  console.log('\n或者在微信开发者工具中:');
  console.log('   调用云函数 managePromptTemplates，参数: { action: "initDefaults" }');
  
  console.log('\n🔍 验证修复效果:');
  console.log('1. 在小程序中选择学生"李四"');
  console.log('2. 生成评语，检查是否使用"四同学"称呼');
  console.log('3. 确保不再出现"小彤同学"等错误姓名');
}

// 主函数
function main() {
  console.log('🔧 提示词模板更新工具\n');
  
  const templates = loadUpdatedTemplates();
  if (templates) {
    generateDeployInstructions(templates);
    
    console.log('\n✨ 修复要点总结:');
    console.log('1. ✅ 添加了姓名强化约束');
    console.log('2. ✅ 增强了变量替换验证');
    console.log('3. ✅ 实现了AI响应后处理');
    console.log('4. ✅ 添加了常见错误姓名替换');
    console.log('5. ✅ 确保评语包含正确学生姓名');
    
    console.log('\n🎯 预期效果:');
    console.log('- 学生"李四" → 称呼"四同学"');
    console.log('- 学生"王小明" → 称呼"明同学"'); 
    console.log('- 不再出现"小彤"、"小华"等训练数据中的示例姓名');
    console.log('- AI严格按照提供的真实学生姓名生成评语');
  }
}

main();