/**
 * 诊断学生数据显示问题
 * 在小程序开发者工具控制台运行
 */

async function debugStudentDisplay() {
  console.log('🔍 开始诊断学生数据显示问题...')
  
  try {
    // 1. 直接查询students集合
    console.log('📊 Step 1: 直接查询students集合')
    const directCount = await wx.cloud.database().collection('students').count()
    console.log(`✅ 数据库中学生数量: ${directCount.total}`)
    
    // 2. 调用dataQuery云函数获取Dashboard统计
    console.log('📊 Step 2: 调用dataQuery云函数')
    const dashboardResult = await wx.cloud.callFunction({
      name: 'dataQuery',
      data: {
        action: 'getDashboardStats'
      }
    })
    
    console.log('✅ 云函数完整响应:', JSON.stringify(dashboardResult, null, 2))
    
    if (dashboardResult.result) {
      console.log('📋 云函数返回的data字段:', dashboardResult.result.data)
      if (dashboardResult.result.data) {
        console.log(`👥 totalUsers: ${dashboardResult.result.data.totalUsers}`)
        console.log(`💬 todayComments: ${dashboardResult.result.data.todayComments}`)  
        console.log(`🤖 aiCalls: ${dashboardResult.result.data.aiCalls}`)
        console.log(`🎓 totalStudents: ${dashboardResult.result.data.totalStudents}`)
      }
    }
    
    // 3. 测试getTotalStudents方法
    console.log('📊 Step 3: 单独测试getTotalStudents')
    const studentsOnlyResult = await wx.cloud.callFunction({
      name: 'dataQuery', 
      data: {
        action: 'getStudents',
        params: { page: 1, limit: 1 }
      }
    })
    
    console.log('🎓 getStudents结果:', studentsOnlyResult.result)
    
    // 4. 检查students集合的实际数据结构
    console.log('📊 Step 4: 检查students集合数据结构')
    const sampleData = await wx.cloud.database().collection('students').limit(3).get()
    console.log('📄 students集合样本数据:')
    sampleData.data.forEach((student, index) => {
      console.log(`   [${index + 1}] ID: ${student._id}`)
      console.log(`       姓名: ${student.name || student.studentName || '未知'}`)
      console.log(`       班级: ${student.className || student.class || '未知'}`)
      console.log(`       字段: ${Object.keys(student).join(', ')}`)
    })
    
    console.log('🎯 诊断完成！')
    
    return {
      直接查询学生数量: directCount.total,
      云函数返回学生数量: dashboardResult.result?.data?.totalStudents || 0,
      数据一致性: directCount.total === (dashboardResult.result?.data?.totalStudents || 0)
    }
    
  } catch (error) {
    console.error('❌ 诊断失败:', error)
    return { error: error.message }
  }
}

// 执行诊断
debugStudentDisplay().then(result => {
  console.log('🎯 最终诊断结果:', result)
})