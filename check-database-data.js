/**
 * 数据库数据检查脚本
 * 在小程序开发者工具控制台中运行
 */

// 检查所有关键集合的数据数量
async function checkAllCollections() {
  console.log('🔍 开始检查数据库各集合数据...')
  
  const collections = [
    'users',           // 用户/教师数据
    'students',        // 学生数据  
    'comments',        // 评语数据
    'ai_usage',        // AI使用统计
    'records',         // 行为记录
    'classes'          // 班级数据
  ]
  
  const results = {}
  
  for (const collection of collections) {
    try {
      console.log(`📊 检查集合: ${collection}`)
      
      // 获取数量
      const countResult = await wx.cloud.database().collection(collection).count()
      console.log(`   数量: ${countResult.total}`)
      
      // 获取前3条样本数据
      const sampleResult = await wx.cloud.database().collection(collection).limit(3).get()
      console.log(`   样本数据数量: ${sampleResult.data.length}`)
      
      if (sampleResult.data.length > 0) {
        console.log(`   第一条数据字段:`, Object.keys(sampleResult.data[0]))
        console.log(`   第一条数据:`, sampleResult.data[0])
      }
      
      results[collection] = {
        count: countResult.total,
        hasData: countResult.total > 0,
        sampleFields: sampleResult.data.length > 0 ? Object.keys(sampleResult.data[0]) : [],
        firstRecord: sampleResult.data[0] || null
      }
      
    } catch (error) {
      console.error(`❌ 检查集合 ${collection} 失败:`, error)
      results[collection] = {
        count: 0,
        hasData: false,
        error: error.message
      }
    }
  }
  
  console.log('\n📋 数据库检查汇总:')
  console.table(results)
  
  // 分析问题
  console.log('\n🔍 问题分析:')
  if (results.users.count === 0) {
    console.log('❌ users集合无数据 - 可能原因:')
    console.log('   1. 小程序用户没有进行登录/注册')
    console.log('   2. 用户注册逻辑有问题')
    console.log('   3. openid获取失败')
  }
  
  if (results.students.count === 0) {
    console.log('❌ students集合无数据 - 可能原因:')
    console.log('   1. 用户没有添加学生')
    console.log('   2. 学生添加功能有问题')
  }
  
  if (results.comments.count === 0) {
    console.log('❌ comments集合无数据 - 可能原因:')
    console.log('   1. 用户没有生成评语')
    console.log('   2. 评语生成功能有问题')
  }
  
  return results
}

// 执行检查
checkAllCollections()