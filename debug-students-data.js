/**
 * 调试学生数据问题
 * 在小程序开发者工具控制台中运行
 */

async function debugStudentsData() {
  console.log('🔍 开始调试学生数据问题...')
  
  try {
    // 1. 直接查询students集合
    console.log('📊 Step 1: 直接查询students集合')
    const studentsCount = await wx.cloud.database().collection('students').count()
    console.log(`   students集合数量: ${studentsCount.total}`)
    
    const studentsData = await wx.cloud.database().collection('students').limit(5).get()
    console.log(`   students样本数据 (${studentsData.data.length}条):`)
    studentsData.data.forEach((student, index) => {
      console.log(`   ${index + 1}. ${student.name} (ID: ${student._id})`)
    })
    
    // 2. 调用dataQuery云函数测试
    console.log('\n📊 Step 2: 测试dataQuery云函数')
    const dataQueryResult = await wx.cloud.callFunction({
      name: 'dataQuery',
      data: {
        action: 'getDashboardStats'
      }
    })
    
    console.log('   dataQuery返回结果:', dataQueryResult.result)
    if (dataQueryResult.result && dataQueryResult.result.data) {
      console.log('   totalStudents字段值:', dataQueryResult.result.data.totalStudents)
    }
    
    // 3. 测试单独的学生统计
    console.log('\n📊 Step 3: 测试单独学生统计')
    const studentStatsResult = await wx.cloud.callFunction({
      name: 'dataQuery',
      data: {
        action: 'getTotalStudents'  // 如果有这个action的话
      }
    })
    console.log('   学生统计结果:', studentStatsResult.result)
    
    // 4. 如果students集合有数据但云函数返回0，检查是否有过滤条件
    if (studentsCount.total > 0 && dataQueryResult.result?.data?.totalStudents === 0) {
      console.log('\n⚠️  警告: 本地有学生数据，但云函数返回0！')
      console.log('   可能原因:')
      console.log('   1. 云函数查询时有teacherId过滤条件')
      console.log('   2. 字段名不匹配')
      console.log('   3. 数据库权限问题')
      
      // 检查students数据的字段结构
      if (studentsData.data.length > 0) {
        console.log('\n📋 学生数据字段结构:')
        console.log('   第一条学生数据的所有字段:', Object.keys(studentsData.data[0]))
        console.log('   完整数据:', studentsData.data[0])
      }
    }
    
    // 5. 创建更多测试学生（如果数量少于3）
    if (studentsCount.total < 3) {
      console.log('\n➕ 创建更多测试学生...')
      const newStudents = [
        {
          name: '测试学生A',
          className: '一年级1班',
          teacherId: 'test_teacher_001',
          studentNumber: 'STU001',
          createTime: new Date(),
          updateTime: new Date(),
          status: 'active'
        },
        {
          name: '测试学生B', 
          className: '二年级1班',
          teacherId: 'test_teacher_002',
          studentNumber: 'STU002',
          createTime: new Date(),
          updateTime: new Date(),
          status: 'active'
        },
        {
          name: '测试学生C',
          className: '三年级1班', 
          teacherId: 'test_teacher_003',
          studentNumber: 'STU003',
          createTime: new Date(),
          updateTime: new Date(),
          status: 'active'
        }
      ]
      
      for (const student of newStudents) {
        try {
          const result = await wx.cloud.database().collection('students').add({
            data: student
          })
          console.log(`   ✅ 创建学生: ${student.name} (ID: ${result._id})`)
        } catch (error) {
          console.log(`   ❌ 创建学生失败: ${student.name} - ${error.message}`)
        }
      }
      
      // 重新统计
      const newCount = await wx.cloud.database().collection('students').count()
      console.log(`   📊 更新后学生总数: ${newCount.total}`)
    }
    
    // 6. 最终验证
    console.log('\n🎯 最终验证...')
    const finalDataQuery = await wx.cloud.callFunction({
      name: 'dataQuery',
      data: {
        action: 'getDashboardStats'
      }
    })
    
    console.log('🏁 最终结果:')
    console.table({
      '数据库中学生数量': studentsCount.total,
      '云函数返回学生数量': finalDataQuery.result?.data?.totalStudents || 0,
      '数据是否一致': studentsCount.total === (finalDataQuery.result?.data?.totalStudents || 0) ? '✅ 是' : '❌ 否'
    })
    
    return {
      dbCount: studentsCount.total,
      cloudFunctionCount: finalDataQuery.result?.data?.totalStudents || 0,
      isConsistent: studentsCount.total === (finalDataQuery.result?.data?.totalStudents || 0)
    }
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error)
    return { error: error.message }
  }
}

// 执行调试
debugStudentsData()