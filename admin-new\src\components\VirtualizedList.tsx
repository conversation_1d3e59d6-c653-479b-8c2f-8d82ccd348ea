import React, { memo, useMemo, useState, useCallback } from 'react'
import { FixedSizeList as List } from 'react-window'
import { Card, Input, Empty, Skeleton } from 'antd'
import { SearchOutlined } from '@ant-design/icons'

interface ListItem {
  id: string
  title: string
  content: string
  time: string
  user: string
  type: string
}

interface VirtualizedListProps {
  data: ListItem[]
  height?: number
  itemHeight?: number
  loading?: boolean
  onItemClick?: (item: ListItem) => void
  searchable?: boolean
}

// 🔥 优化的列表项组件 - 使用memo防止不必要渲染
const ListItemComponent = memo<{
  index: number
  style: React.CSSProperties
  data: {
    items: ListItem[]
    onItemClick?: (item: ListItem) => void
  }
}>(({ index, style, data }) => {
  const item = data.items[index]
  
  const handleClick = useCallback(() => {
    data.onItemClick?.(item)
  }, [data.onItemClick, item])

  return (
    <div style={style} className="p-2">
      <div 
        className="bg-white rounded-lg p-4 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer"
        onClick={handleClick}
      >
        <div className="flex justify-between items-start mb-2">
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 text-sm truncate flex-1 mr-2">
            {item.title}
          </h4>
          <span className="text-xs text-gray-500 dark:text-gray-300 whitespace-nowrap">
            {item.time}
          </span>
        </div>
        <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-2">
          {item.content}
        </p>
        <div className="flex justify-between items-center">
          <span className="text-xs text-blue-600 font-medium">
            {item.user}
          </span>
          <span className={`text-xs px-2 py-1 rounded-full ${
            item.type === 'urgent' ? 'bg-red-100 text-red-600' :
            item.type === 'important' ? 'bg-yellow-100 text-yellow-600' :
            'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
          }`}>
            {item.type}
          </span>
        </div>
      </div>
    </div>
  )
})

ListItemComponent.displayName = 'ListItemComponent'

// 🔥 虚拟化列表组件
const VirtualizedList: React.FC<VirtualizedListProps> = ({
  data,
  height = 400,
  itemHeight = 120,
  loading = false,
  onItemClick,
  searchable = true
}) => {
  const [searchTerm, setSearchTerm] = useState('')

  // 🔥 使用useMemo优化搜索过滤
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) return data
    
    const term = searchTerm.toLowerCase()
    return data.filter(item => 
      item.title.toLowerCase().includes(term) ||
      item.content.toLowerCase().includes(term) ||
      item.user.toLowerCase().includes(term)
    )
  }, [data, searchTerm])

  // 🔥 优化搜索处理函数
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }, [])

  // 🔥 Memoized的列表数据结构
  const listData = useMemo(() => ({
    items: filteredData,
    onItemClick
  }), [filteredData, onItemClick])

  if (loading) {
    return (
      <Card>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} active paragraph={{ rows: 2 }} />
          ))}
        </div>
      </Card>
    )
  }

  return (
    <Card 
      title={
        <div className="flex justify-between items-center">
          <span>数据列表 ({filteredData.length})</span>
          {searchable && (
            <Input
              placeholder="搜索..."
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={handleSearch}
              className="w-64"
              allowClear
            />
          )}
        </div>
      }
      className="h-full"
    >
      {filteredData.length === 0 ? (
        <Empty description={searchTerm ? "没有找到匹配的数据" : "暂无数据"} />
      ) : (
        <List
          height={height}
          itemCount={filteredData.length}
          itemSize={itemHeight}
          itemData={listData}
          className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          {ListItemComponent}
        </List>
      )}
      
      {/* 性能提示 */}
      <div className="mt-4 text-xs text-gray-500 dark:text-gray-300 flex items-center">
        <span className="mr-2">🚀</span>
        虚拟滚动已启用 - 仅渲染可见区域，支持海量数据流畅滚动
      </div>
    </Card>
  )
}

export default memo(VirtualizedList)