/**
 * 检查现有system_config数据并升级
 * 运行方式: node check-existing-data.js
 */

const tcb = require('@cloudbase/node-sdk')

// 云开发配置
const app = tcb.init({
  env: 'cloud1-4g85f8xlb8166ff1', // 你的云开发环境ID
  region: 'ap-shanghai'
})

const db = app.database()

async function checkAndUpgradeExistingData() {
  try {
    console.log('🔍 检查现有system_config数据...')
    
    // 1. 查看所有system_config记录
    console.log('\n📋 1. 查看所有system_config记录:')
    const { data: allConfigs } = await db.collection('system_config').get()
    console.log(`总共找到 ${allConfigs.length} 条记录`)
    
    allConfigs.forEach((config, index) => {
      console.log(`\n记录 ${index + 1}:`)
      console.log(`  ID: ${config._id}`)
      console.log(`  类型: ${config.type || '未设置'}`)
      console.log(`  模型: ${config.model || '未设置'}`)
      console.log(`  提供商: ${config.provider || '未设置'}`)
      console.log(`  状态: ${config.status || '未设置'}`)
      console.log(`  输入定价: ${config.inputPrice !== undefined ? '¥' + config.inputPrice : '❌ 未设置'}`)
      console.log(`  输出定价: ${config.outputPrice !== undefined ? '¥' + config.outputPrice : '❌ 未设置'}`)
      console.log(`  创建时间: ${config.createTime ? new Date(config.createTime).toLocaleString() : '未设置'}`)
    })
    
    // 2. 专门查看ai_config类型的记录
    console.log('\n\n📋 2. 查看AI配置记录:')
    const { data: aiConfigs } = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    if (aiConfigs.length === 0) {
      console.log('❌ 没找到type为"ai_config"的记录')
      console.log('💡 建议: 可能你的AI配置记录没有type字段，我们来查找可能的AI配置记录')
      
      // 查找可能是AI配置的记录
      const possibleAiConfigs = allConfigs.filter(config => 
        config.model || config.provider || config.apiKey || config.temperature
      )
      
      if (possibleAiConfigs.length > 0) {
        console.log(`\n🎯 找到 ${possibleAiConfigs.length} 条可能的AI配置记录:`)
        possibleAiConfigs.forEach((config, index) => {
          console.log(`\n可能的AI配置 ${index + 1}:`)
          console.log(`  ID: ${config._id}`)
          console.log(`  模型: ${config.model || '未设置'}`)
          console.log(`  提供商: ${config.provider || '未设置'}`)
          console.log(`  API密钥: ${config.apiKey ? config.apiKey.substr(0, 10) + '...' : '未设置'}`)
          console.log(`  温度参数: ${config.temperature || '未设置'}`)
        })
        
        console.log('\n✅ 我可以帮你升级这些记录，添加type和定价字段')
        return { needsUpgrade: true, configs: possibleAiConfigs }
      }
    } else {
      console.log(`✅ 找到 ${aiConfigs.length} 条AI配置记录`)
      
      let needsUpgrade = false
      aiConfigs.forEach((config, index) => {
        console.log(`\nAI配置 ${index + 1}:`)
        console.log(`  ID: ${config._id}`)
        console.log(`  模型: ${config.model}`)
        console.log(`  提供商: ${config.provider}`)
        
        if (config.inputPrice === undefined || config.outputPrice === undefined) {
          console.log(`  ❌ 缺少定价字段`)
          needsUpgrade = true
        } else {
          console.log(`  ✅ 输入定价: ¥${config.inputPrice}`)
          console.log(`  ✅ 输出定价: ¥${config.outputPrice}`)
        }
      })
      
      if (needsUpgrade) {
        console.log('\n💡 发现有配置缺少定价字段，需要升级')
        return { needsUpgrade: true, configs: aiConfigs }
      } else {
        console.log('\n🎉 所有AI配置都已包含定价字段，无需升级')
        return { needsUpgrade: false, configs: aiConfigs }
      }
    }
    
  } catch (error) {
    console.error('❌ 检查数据失败:', error)
    throw error
  }
}

async function upgradeExistingConfigs(configs) {
  console.log('\n🔧 开始升级现有配置...')
  
  for (let i = 0; i < configs.length; i++) {
    const config = configs[i]
    console.log(`\n升级配置 ${i + 1}/${configs.length}: ${config._id}`)
    
    // 准备升级数据
    const upgradeData = {}
    
    // 确保有type字段
    if (!config.type) {
      upgradeData.type = 'ai_config'
      console.log('  ✅ 添加type字段')
    }
    
    // 确保有定价字段
    if (config.inputPrice === undefined) {
      // 根据提供商设置默认定价
      let defaultInputPrice = 0.001
      switch (config.provider) {
        case 'bytedance':
          defaultInputPrice = 0.001
          break
        case 'openai':
          defaultInputPrice = 0.0075
          break
        case 'anthropic':
          defaultInputPrice = 0.003
          break
        default:
          defaultInputPrice = 0.001
      }
      upgradeData.inputPrice = defaultInputPrice
      console.log(`  ✅ 添加输入定价: ¥${defaultInputPrice}`)
    }
    
    if (config.outputPrice === undefined) {
      // 根据提供商设置默认定价
      let defaultOutputPrice = 0.002
      switch (config.provider) {
        case 'bytedance':
          defaultOutputPrice = 0.002
          break
        case 'openai':
          defaultOutputPrice = 0.030
          break
        case 'anthropic':
          defaultOutputPrice = 0.015
          break
        default:
          defaultOutputPrice = 0.002
      }
      upgradeData.outputPrice = defaultOutputPrice
      console.log(`  ✅ 添加输出定价: ¥${defaultOutputPrice}`)
    }
    
    // 确保有时间戳
    if (!config.updateTime) {
      upgradeData.updateTime = Date.now()
      console.log('  ✅ 添加更新时间')
    }
    
    if (!config.createTime) {
      upgradeData.createTime = Date.now()
      console.log('  ✅ 添加创建时间')
    }
    
    // 执行升级
    if (Object.keys(upgradeData).length > 0) {
      await db.collection('system_config').doc(config._id).update(upgradeData)
      console.log(`  🎉 配置 ${config._id} 升级完成`)
    } else {
      console.log(`  ⏭️ 配置 ${config._id} 无需升级`)
    }
  }
  
  console.log('\n✅ 所有配置升级完成!')
}

async function main() {
  console.log('🚀 开始检查和升级现有数据...')
  
  const result = await checkAndUpgradeExistingData()
  
  if (result.needsUpgrade) {
    console.log('\n❓ 是否要升级这些配置？(自动执行升级)')
    await upgradeExistingConfigs(result.configs)
    
    // 验证升级结果
    console.log('\n🔍 验证升级结果...')
    const { data: verifyConfigs } = await db.collection('system_config').where({
      type: 'ai_config'
    }).get()
    
    console.log(`\n✅ 升级完成! 现在有 ${verifyConfigs.length} 条AI配置:`)
    verifyConfigs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.provider}/${config.model}`)
      console.log(`   输入定价: ¥${config.inputPrice}`)
      console.log(`   输出定价: ¥${config.outputPrice}`)
      console.log(`   状态: ${config.status}`)
    })
  }
  
  console.log('\n🎉 现在可以在管理后台测试AI配置功能了!')
  console.log('💡 建议步骤:')
  console.log('1. 启动管理后台: cd admin-new && npm run dev')
  console.log('2. 访问AI配置页面')
  console.log('3. 查看系统参数选项卡中的定价字段')
  console.log('4. 尝试修改定价并保存')
  console.log('5. 测试手动同步功能')
}

// 运行主程序
if (require.main === module) {
  main()
    .then(() => {
      console.log('✅ 处理完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 处理出错:', error)
      process.exit(1)
    })
}

module.exports = { checkAndUpgradeExistingData, upgradeExistingConfigs }