{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}, "fromGalleryId": "upstash.context7"}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "fromGalleryId": "executeautomation.mcp-playwright"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "desktop-commander": {"command": "npx", "args": ["-y", "desktop-commander"], "env": {}, "fromGalleryId": "wonderwhy-er.<PERSON>"}}}