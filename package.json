{"name": "pingy<PERSON>gg<PERSON>jun-miniprogram", "version": "1.0.0", "description": "评语灵感君1.0 - AI智能评语生成小程序 (2025现代化版本)", "main": "app.js", "scripts": {"dev": "echo '请在微信开发者工具中打开项目'", "build": "npm run lint && npm run type-check && echo '请在微信开发者工具中构建npm'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix", "type-check": "tsc --noEmit", "prepare": "husky install", "format": "prettier --write \"**/*.{js,ts,json,md}\"", "format:check": "prettier --check \"**/*.{js,ts,json,md}\"", "deploy:dev": "echo '部署到开发环境'", "deploy:prod": "echo '部署到生产环境'"}, "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "@vant/weapp": "^1.11.6", "dayjs": "^1.11.10", "echarts": "^5.4.3", "lodash-es": "^4.17.21", "tslib": "^2.6.2", "zod": "^3.22.4", "zrender": "^5.4.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.1.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "keywords": ["miniprogram", "wechat", "ai", "education", "comment", "teacher", "typescript", "modern"], "author": "评语灵感君团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/pingyulingganjun.git"}, "engines": {"node": ">=18.0.0"}}