/**
 * AI模型配置表初始化脚本
 * 用于存储不同AI模型的配置和定价信息
 */

// 创建AI配置集合
db.createCollection('ai_configs');

// 创建索引
db.ai_configs.createIndex({ "status": 1, "createTime": -1 });
db.ai_configs.createIndex({ "provider": 1, "model": 1 });
db.ai_configs.createIndex({ "isDefault": 1 });

// 插入默认豆包模型配置
db.ai_configs.insertOne({
  name: '豆包Flash模型',
  provider: 'bytedance',
  model: 'doubao-seed-1-6-flash-250715',
  status: 'active',
  config: {
    apiKey: '',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    maxTokens: 2000,
    temperature: 0.7,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    enableStream: false,
    enableCache: true,
    timeout: 30
  },
  inputPrice: 0.00007,   // 输入定价：0.00007元/千tokens
  outputPrice: 0.0003,   // 输出定价：0.0003元/千tokens
  usage: 0,
  totalCost: 0,
  createTime: new Date(),
  updateTime: new Date(),
  isDefault: true
});

// 插入豆包Pro模型配置
db.ai_configs.insertOne({
  name: '豆包Pro 32K',
  provider: 'bytedance',
  model: 'doubao-pro-32k',
  status: 'inactive',
  config: {
    apiKey: '',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    maxTokens: 2000,
    temperature: 0.7,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    enableStream: false,
    enableCache: true,
    timeout: 30
  },
  inputPrice: 0.00075,   // 输入定价：0.00075元/千tokens
  outputPrice: 0.0030,   // 输出定价：0.0030元/千tokens
  usage: 0,
  totalCost: 0,
  createTime: new Date(),
  updateTime: new Date(),
  isDefault: false
});

// 插入GPT-4示例配置（供参考）
db.ai_configs.insertOne({
  name: 'GPT-4 Turbo',
  provider: 'openai',
  model: 'gpt-4-turbo',
  status: 'inactive',
  config: {
    apiKey: '',
    baseURL: 'https://api.openai.com/v1',
    maxTokens: 2000,
    temperature: 0.7,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    enableStream: false,
    enableCache: true,
    timeout: 30
  },
  inputPrice: 0.01,      // 输入定价：0.01元/千tokens（示例）
  outputPrice: 0.03,     // 输出定价：0.03元/千tokens（示例）
  usage: 0,
  totalCost: 0,
  createTime: new Date(),
  updateTime: new Date(),
  isDefault: false
});

// 插入GPT-3.5示例配置（供参考）
db.ai_configs.insertOne({
  name: 'GPT-3.5 Turbo',
  provider: 'openai',
  model: 'gpt-3.5-turbo',
  status: 'inactive',
  config: {
    apiKey: '',
    baseURL: 'https://api.openai.com/v1',
    maxTokens: 2000,
    temperature: 0.7,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    enableStream: false,
    enableCache: true,
    timeout: 30
  },
  inputPrice: 0.0015,    // 输入定价：0.0015元/千tokens（示例）
  outputPrice: 0.002,    // 输出定价：0.002元/千tokens（示例）
  usage: 0,
  totalCost: 0,
  createTime: new Date(),
  updateTime: new Date(),
  isDefault: false
});

console.log('AI模型配置表初始化完成');

// 数据库权限设置
// 管理员可以读写所有AI配置
// 普通用户只能读取非敏感配置信息（不包含API密钥）
