# AI配置数据升级脚本
Write-Host "🚀 开始检查和升级现有AI配置数据..." -ForegroundColor Green
Write-Host ""

Write-Host "📋 第一步：检查现有数据" -ForegroundColor Yellow
node simple-check.js

Write-Host ""
Write-Host "🎉 升级完成！" -ForegroundColor Green
Write-Host ""
Write-Host "💡 接下来可以：" -ForegroundColor Cyan
Write-Host "1. 启动管理后台: cd admin-new && npm run dev" -ForegroundColor White
Write-Host "2. 访问AI配置页面测试功能" -ForegroundColor White
Write-Host ""
Read-Host "按回车键继续..."